// 多语言配置文件
const i18n = {
    // 中文
    zh: {
    // Site
    siteTitle: "GoMyHire 司机FAQ",
    siteSubtitle: "司机FAQ系统",
        // 导航和界面
        categories: "问题分类",
        quickAccess: "快速访问",
        favorites: "收藏夹",
        recent: "最近浏览",
        popular: "热门问题",
        searchResults: "搜索结果",
        loading: "加载中...",
        
        // 欢迎页面
        welcomeTitle: "欢迎使用GoMyHire司机FAQ系统",
        welcomeDesc: "这里汇集了司机朋友们最常遇到的问题和详细解答。您可以通过分类浏览或搜索快速找到需要的信息。",
        searchFeature: "智能搜索",
        searchFeatureDesc: "支持问题编号、关键词搜索",
        mobileFeature: "移动优化",
        mobileFeatureDesc: "完美适配手机和平板设备",
        multiLangFeature: "多语言支持",
        multiLangFeatureDesc: "中文、英文、马来文三语切换",
        quickStart: "快速开始",
        
        // 分类名称
        registration: "司机注册",
        appUsage: "APP使用",
        orderManagement: "订单管理",
        customerService: "客户沟通",
        payment: "支付财务",
        rating: "评价评分",
        training: "培训考试",
        vehicle: "车辆管理",
        safety: "安全合规",
        emergency: "紧急处理",
        
        // 操作按钮
        addToFavorites: "添加到收藏",
        removeFromFavorites: "取消收藏",
        share: "分享",
        feedback: "反馈",
        backToHome: "返回首页",
        previousQuestion: "上一题",
        nextQuestion: "下一题",
        
        // 搜索相关
        searchPlaceholder: "搜索问题编号或关键词...",
        searchNoResults: "未找到相关问题",
        searchResultsCount: "找到 {count} 个相关问题",
        
        // 相关问题
        relatedQuestions: "相关问题",
        seeMore: "查看更多",
        
        // 优先级
        priorityHigh: "高频",
        priorityMedium: "常见",
        priorityLow: "一般",
        
        // 底部信息
        contact: "联系我们",
        customerService: "客服热线：400-XXX-XXXX",
        email: "邮箱：<EMAIL>",
        quickLinks: "快速链接",
        driverApp: "司机APP下载",
        driverPortal: "司机门户",
        trainingCenter: "培训中心",
        allRightsReserved: "版权所有",
        
        // 错误信息
        errorLoadingData: "数据加载失败，请刷新页面重试",
        errorNotFound: "页面未找到",
        errorNetwork: "网络连接异常，请检查网络设置",
        
        // 提示信息
        addedToFavorites: "已添加到收藏夹",
        removedFromFavorites: "已从收藏夹移除",
        copiedToClipboard: "链接已复制到剪贴板",
        thankYouForFeedback: "感谢您的反馈",
    // 时间文本
    justNow: "现在",
    minutesAgo: "分钟前",

        // 欢迎页面动态文本
        welcomeFAQTitle: "欢迎使用GoMyHire司机FAQ",
        welcomeFAQDesc: "选择分类快速找到您需要的答案",
        questionsCount: "个问题",
        totalQuestions: "共 {count} 个问题",

        // 页面导航
        backToHomePage: "← 返回首页",
        unknownCategory: "未知分类",
        relatedQuestionsTitle: "相关问题",

        // 主题切换
        switchToDarkTheme: "切换到暗色主题",
        switchToLightTheme: "切换到亮色主题",

        // 搜索结果页面
        searchResultsTitle: "搜索结果",
        searchFor: "搜索",
        foundResults: "找到 {count} 个结果",
        noResultsFound: "没有找到相关结果",
        noResultsDesc: "请尝试使用其他关键词或浏览分类查找相关问题。",
        backToHomeBtn: "返回首页"
    },
    
    // 英文
    en: {
    // Site
    siteTitle: "GoMyHire Driver FAQ",
    siteSubtitle: "Driver FAQ System",
        // Navigation and Interface
        categories: "Categories",
        quickAccess: "Quick Access",
        favorites: "Favorites",
        recent: "Recent",
        popular: "Popular",
        searchResults: "Search Results",
        loading: "Loading...",
        
        // Welcome Page
        welcomeTitle: "Welcome to GoMyHire Driver FAQ System",
        welcomeDesc: "Here you'll find answers to the most common questions drivers encounter. Browse by category or search to quickly find the information you need.",
        searchFeature: "Smart Search",
        searchFeatureDesc: "Search by question ID or keywords",
        mobileFeature: "Mobile Optimized",
        mobileFeatureDesc: "Perfect for phones and tablets",
        multiLangFeature: "Multi-language",
        multiLangFeatureDesc: "Chinese, English, Malay support",
        quickStart: "Quick Start",
        
        // Category Names
        registration: "Driver Registration",
        appUsage: "App Usage",
        orderManagement: "Order Management",
        customerService: "Customer Communication",
        payment: "Payment & Finance",
        rating: "Rating & Review",
        training: "Training & Exam",
        vehicle: "Vehicle Management",
        safety: "Safety & Compliance",
        emergency: "Emergency Handling",
        
        // Action Buttons
        addToFavorites: "Add to Favorites",
        removeFromFavorites: "Remove from Favorites",
        share: "Share",
        feedback: "Feedback",
        backToHome: "Back to Home",
        previousQuestion: "Previous",
        nextQuestion: "Next",
        
        // Search Related
        searchPlaceholder: "Search by question ID or keywords...",
        searchNoResults: "No results found",
        searchResultsCount: "Found {count} related questions",
        
        // Related Questions
        relatedQuestions: "Related Questions",
        seeMore: "See More",
        
        // Priority
        priorityHigh: "High Frequency",
        priorityMedium: "Common",
        priorityLow: "General",
        
        // Footer
        contact: "Contact Us",
        customerService: "Hotline: 400-XXX-XXXX",
        email: "Email: <EMAIL>",
        quickLinks: "Quick Links",
        driverApp: "Driver App Download",
        driverPortal: "Driver Portal",
        trainingCenter: "Training Center",
        allRightsReserved: "All Rights Reserved",
        
        // Error Messages
        errorLoadingData: "Failed to load data, please refresh and try again",
        errorNotFound: "Page not found",
        errorNetwork: "Network connection error, please check your connection",
        
        // Notifications
        addedToFavorites: "Added to favorites",
        removedFromFavorites: "Removed from favorites",
        copiedToClipboard: "Link copied to clipboard",
        thankYouForFeedback: "Thank you for your feedback",
    // time text
    justNow: "just now",
    minutesAgo: "minutes ago",

        // Welcome page dynamic text
        welcomeFAQTitle: "Welcome to GoMyHire Driver FAQ",
        welcomeFAQDesc: "Select a category to quickly find the answers you need",
        questionsCount: "questions",
        totalQuestions: "Total {count} questions",

        // Page navigation
        backToHomePage: "← Back to Home",
        unknownCategory: "Unknown Category",
        relatedQuestionsTitle: "Related Questions",

        // Theme switching
        switchToDarkTheme: "Switch to dark theme",
        switchToLightTheme: "Switch to light theme",

        // Search results page
        searchResultsTitle: "Search Results",
        searchFor: "Search for",
        foundResults: "Found {count} results",
        noResultsFound: "No results found",
        noResultsDesc: "Please try using other keywords or browse categories to find related questions.",
        backToHomeBtn: "Back to Home"
    },
    
    // 马来文
    ms: {
    // Site
    siteTitle: "GoMyHire FAQ Pemandu",
    siteSubtitle: "Sistem FAQ Pemandu",
        // Navigasi dan Antara Muka
        categories: "Kategori",
        quickAccess: "Akses Pantas",
        favorites: "Kegemaran",
        recent: "Terkini",
        popular: "Popular",
        searchResults: "Hasil Carian",
        loading: "Memuatkan...",
        
        // Halaman Selamat Datang
        welcomeTitle: "Selamat Datang ke Sistem FAQ Pemandu GoMyHire",
        welcomeDesc: "Di sini anda akan menemui jawapan kepada soalan yang paling kerap dihadapi oleh pemandu. Layari mengikut kategori atau cari untuk mendapatkan maklumat yang diperlukan dengan pantas.",
        searchFeature: "Carian Pintar",
        searchFeatureDesc: "Cari mengikut ID soalan atau kata kunci",
        mobileFeature: "Dioptimumkan untuk Mudah Alih",
        mobileFeatureDesc: "Sempurna untuk telefon dan tablet",
        multiLangFeature: "Pelbagai Bahasa",
        multiLangFeatureDesc: "Sokongan Cina, Inggeris, Melayu",
        quickStart: "Mula Pantas",
        
        // Nama Kategori
        registration: "Pendaftaran Pemandu",
        appUsage: "Penggunaan Aplikasi",
        orderManagement: "Pengurusan Pesanan",
        customerService: "Komunikasi Pelanggan",
        payment: "Pembayaran & Kewangan",
        rating: "Penilaian & Ulasan",
        training: "Latihan & Peperiksaan",
        vehicle: "Pengurusan Kenderaan",
        safety: "Keselamatan & Pematuhan",
        emergency: "Pengendalian Kecemasan",
        
        // Butang Tindakan
        addToFavorites: "Tambah ke Kegemaran",
        removeFromFavorites: "Buang dari Kegemaran",
        share: "Kongsi",
        feedback: "Maklum Balas",
        backToHome: "Kembali ke Utama",
        previousQuestion: "Sebelumnya",
        nextQuestion: "Seterusnya",
        
        // Berkaitan Carian
        searchPlaceholder: "Cari mengikut ID soalan atau kata kunci...",
        searchNoResults: "Tiada hasil ditemui",
        searchResultsCount: "Ditemui {count} soalan berkaitan",
        
        // Soalan Berkaitan
        relatedQuestions: "Soalan Berkaitan",
        seeMore: "Lihat Lagi",
        
        // Keutamaan
        priorityHigh: "Kekerapan Tinggi",
        priorityMedium: "Biasa",
        priorityLow: "Am",
        
        // Footer
        contact: "Hubungi Kami",
        customerService: "Talian Hotline: 400-XXX-XXXX",
        email: "E-mel: <EMAIL>",
        quickLinks: "Pautan Pantas",
        driverApp: "Muat Turun Aplikasi Pemandu",
        driverPortal: "Portal Pemandu",
        trainingCenter: "Pusat Latihan",
        allRightsReserved: "Hak Cipta Terpelihara",
        
        // Mesej Ralat
        errorLoadingData: "Gagal memuatkan data, sila muat semula dan cuba lagi",
        errorNotFound: "Halaman tidak ditemui",
        errorNetwork: "Ralat sambungan rangkaian, sila periksa sambungan anda",
        
        // Pemberitahuan
        addedToFavorites: "Ditambah ke kegemaran",
        removedFromFavorites: "Dibuang dari kegemaran",
        copiedToClipboard: "Pautan disalin ke papan keratan",
        thankYouForFeedback: "Terima kasih atas maklum balas anda",
    // masa
    justNow: "baru saja",
    minutesAgo: "minit yang lalu",

        // Teks dinamik halaman selamat datang
        welcomeFAQTitle: "Selamat Datang ke FAQ Pemandu GoMyHire",
        welcomeFAQDesc: "Pilih kategori untuk mencari jawapan yang anda perlukan dengan pantas",
        questionsCount: "soalan",
        totalQuestions: "Jumlah {count} soalan",

        // Navigasi halaman
        backToHomePage: "← Kembali ke Utama",
        unknownCategory: "Kategori Tidak Diketahui",
        relatedQuestionsTitle: "Soalan Berkaitan",

        // Penukaran tema
        switchToDarkTheme: "Tukar ke tema gelap",
        switchToLightTheme: "Tukar ke tema terang",

        // Halaman hasil carian
        searchResultsTitle: "Hasil Carian",
        searchFor: "Cari untuk",
        foundResults: "Ditemui {count} hasil",
        noResultsFound: "Tiada hasil ditemui",
        noResultsDesc: "Sila cuba menggunakan kata kunci lain atau layari kategori untuk mencari soalan berkaitan.",
        backToHomeBtn: "Kembali ke Utama"
    }
};

// 多语言管理器
class I18nManager {
    constructor() {
        this.currentLang = localStorage.getItem('language') || 'zh';
        this.translations = i18n;
    }
    
    // 设置语言
    setLanguage(lang) {
        if (this.translations[lang]) {
            this.currentLang = lang;
            localStorage.setItem('language', lang);
            this.updatePageTexts();
            this.updateDocumentLang();
        }
    }
    
    // 获取翻译文本
    t(key, params = {}) {
        let text = this.translations[this.currentLang][key] || key;
        
        // 替换参数
        Object.keys(params).forEach(param => {
            text = text.replace(`{${param}}`, params[param]);
        });
        
        return text;
    }
    
    // 更新页面所有文本
    updatePageTexts() {
        const elements = document.querySelectorAll('[data-i18n]');
        elements.forEach(element => {
            const key = element.getAttribute('data-i18n');
            element.textContent = this.t(key);
        });
        
        // 更新占位符文本
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.placeholder = this.t('searchPlaceholder');
        }

        // 更新文档标题（如果有翻译）
        if (this.translations[this.currentLang]['siteTitle']) {
            document.title = this.t('siteTitle');
        }

        // 更新可选的局部标题属性 data-i18n-title
        const titleElements = document.querySelectorAll('[data-i18n-title]');
        titleElements.forEach(el => {
            const key = el.getAttribute('data-i18n-title');
            el.textContent = this.t(key);
        });
    }
    
    // 更新文档语言属性
    updateDocumentLang() {
        document.documentElement.lang = this.currentLang;
    }
    
    // 获取当前语言
    getCurrentLanguage() {
        return this.currentLang;
    }

    // 格式化消息时间：优先显示相对时间（刚刚、几分钟前），否则显示本地化时间
    formatMessageTime(date) {
        if (!date) return '';
        const d = (date instanceof Date) ? date : new Date(date);
        const now = new Date();
        const diffMs = now - d;
        const diffSec = Math.round(diffMs / 1000);
        const diffMin = Math.round(diffSec / 60);

        const lang = this.getCurrentLanguage();

        // 刚刚（小于 45 秒）
        if (diffSec < 45) {
            return this.t('justNow');
        }

        // 分钟级别（小于 60 分钟）
        if (diffMin < 60) {
            // 使用 Intl.RelativeTimeFormat 如果可用
            try {
                if (Intl && Intl.RelativeTimeFormat) {
                    const rtf = new Intl.RelativeTimeFormat(lang, { numeric: 'auto' });
                    return rtf.format(-diffMin, 'minute');
                }
            } catch (e) {
                // fallback to simple string
                return `${diffMin} ${this.t('minutesAgo') || '分钟前'}`;
            }
        }

        // 否则使用本地时间格式（仅显示时间或日期时间）
        try {
            const dtf = new Intl.DateTimeFormat(lang, { hour: '2-digit', minute: '2-digit' });
            return dtf.format(d);
        } catch (e) {
            return d.toLocaleTimeString();
        }
    }
}

// 导出多语言管理器
window.I18nManager = I18nManager;
