// 性能优化管理器
// 包括连接池、请求节流、内存管理、预取策略

class PerformanceOptimizer {
    constructor(config = {}) {
        this.config = {
            maxConcurrentRequests: config.maxConcurrentRequests || 3,
            requestTimeoutMs: config.requestTimeoutMs || 10000,
            memoryThreshold: config.memoryThreshold || 50 * 1024 * 1024, // 50MB
            prefetchEnabled: config.prefetchEnabled || true,
            throttleWindowMs: config.throttleWindowMs || 1000,
            maxRequestsPerWindow: config.maxRequestsPerWindow || 5,
            ...config
        };

        // 连接池管理
        this.connectionPool = new ConnectionPoolManager(this.config);
        
        // 请求节流器
        this.throttler = new RequestThrottler(this.config);
        
        // 内存管理器
        this.memoryManager = new MemoryManager(this.config);
        
        // 预取管理器
        this.prefetcher = new PrefetchManager(this.config);
        
        // 性能指标收集
        this.metrics = new PerformanceMetrics();
        
        console.log('✅ 性能优化器初始化完成');
    }

    // 优化的API请求方法
    async optimizedRequest(url, options = {}) {
        const startTime = performance.now();
        
        try {
            // 1. 检查节流限制
            if (!this.throttler.canMakeRequest()) {
                throw new Error('请求频率超限，请稍后重试');
            }

            // 2. 内存检查
            if (!this.memoryManager.canAllocateMemory()) {
                await this.memoryManager.cleanup();
            }

            // 3. 获取连接
            const connection = await this.connectionPool.getConnection();
            
            // 4. 执行请求
            const response = await this.executeRequest(url, options, connection);
            
            // 5. 释放连接
            this.connectionPool.releaseConnection(connection);
            
            // 6. 记录指标
            const duration = performance.now() - startTime;
            this.metrics.recordRequest('success', duration, response.status);
            
            return response;

        } catch (error) {
            const duration = performance.now() - startTime;
            this.metrics.recordRequest('error', duration, 0, error.message);
            throw error;
        }
    }

    // 执行实际请求
    async executeRequest(url, options, connection) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
            controller.abort();
        }, this.config.requestTimeoutMs);

        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            return response;
            
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }

    // 预取相关搜索结果
    async prefetchRelatedQueries(query, language) {
        if (!this.config.prefetchEnabled) return;
        
        try {
            const relatedQueries = this.generateRelatedQueries(query, language);
            await this.prefetcher.prefetchQueries(relatedQueries, language);
        } catch (error) {
            console.debug('预取失败:', error);
        }
    }

    // 生成相关查询
    generateRelatedQueries(query, language) {
        const related = [];
        
        // 简单的相关查询生成逻辑
        if (language === 'zh') {
            // 中文：截取和扩展
            if (query.length > 2) {
                related.push(query.substring(0, 2)); // 前缀
                related.push(query + '问题'); // 后缀
            }
        } else {
            // 英文：词根和同义词
            const words = query.toLowerCase().split(' ');
            words.forEach(word => {
                if (word.length > 3) {
                    related.push(word);
                }
            });
        }
        
        return related.slice(0, 3); // 限制数量
    }

    // 获取性能报告
    getPerformanceReport() {
        return {
            timestamp: new Date().toISOString(),
            connectionPool: this.connectionPool.getStats(),
            throttler: this.throttler.getStats(),
            memory: this.memoryManager.getStats(),
            prefetcher: this.prefetcher.getStats(),
            requests: this.metrics.getStats()
        };
    }

    // 清理资源
    cleanup() {
        this.connectionPool.cleanup();
        this.memoryManager.cleanup();
        this.prefetcher.cleanup();
        this.metrics.reset();
    }
}

// 连接池管理器
class ConnectionPoolManager {
    constructor(config) {
        this.maxConnections = config.maxConcurrentRequests || 3;
        this.activeConnections = 0;
        this.waitingQueue = [];
        this.stats = {
            totalRequests: 0,
            activeConnections: 0,
            queueLength: 0,
            avgWaitTime: 0
        };
    }

    async getConnection() {
        this.stats.totalRequests++;
        
        if (this.activeConnections < this.maxConnections) {
            this.activeConnections++;
            this.stats.activeConnections = this.activeConnections;
            return { id: Date.now() };
        }

        // 连接池满，加入等待队列
        return new Promise((resolve) => {
            const startWait = Date.now();
            this.waitingQueue.push({
                resolve,
                startWait
            });
            this.stats.queueLength = this.waitingQueue.length;
        });
    }

    releaseConnection(connection) {
        this.activeConnections--;
        this.stats.activeConnections = this.activeConnections;

        if (this.waitingQueue.length > 0) {
            const { resolve, startWait } = this.waitingQueue.shift();
            const waitTime = Date.now() - startWait;
            
            // 更新平均等待时间
            this.stats.avgWaitTime = (this.stats.avgWaitTime + waitTime) / 2;
            this.stats.queueLength = this.waitingQueue.length;
            
            this.activeConnections++;
            this.stats.activeConnections = this.activeConnections;
            
            resolve({ id: Date.now() });
        }
    }

    getStats() {
        return { ...this.stats };
    }

    cleanup() {
        this.waitingQueue.forEach(({ resolve }) => {
            resolve(new Error('连接池已清理'));
        });
        this.waitingQueue = [];
        this.activeConnections = 0;
    }
}

// 请求节流器
class RequestThrottler {
    constructor(config) {
        this.windowMs = config.throttleWindowMs || 1000;
        this.maxRequests = config.maxRequestsPerWindow || 5;
        this.requests = [];
        this.stats = {
            totalRequests: 0,
            blockedRequests: 0,
            currentWindow: 0
        };
    }

    canMakeRequest() {
        const now = Date.now();
        
        // 清理过期请求
        this.requests = this.requests.filter(timestamp => 
            now - timestamp < this.windowMs
        );

        this.stats.totalRequests++;
        this.stats.currentWindow = this.requests.length;

        if (this.requests.length >= this.maxRequests) {
            this.stats.blockedRequests++;
            return false;
        }

        this.requests.push(now);
        return true;
    }

    getStats() {
        return { ...this.stats };
    }
}

// 内存管理器
class MemoryManager {
    constructor(config) {
        this.threshold = config.memoryThreshold || 50 * 1024 * 1024;
        this.cleanupCallbacks = [];
        this.stats = {
            currentUsage: 0,
            cleanupCount: 0,
            lastCleanup: null
        };
    }

    canAllocateMemory() {
        this.updateMemoryUsage();
        return this.stats.currentUsage < this.threshold;
    }

    updateMemoryUsage() {
        if (performance.memory) {
            this.stats.currentUsage = performance.memory.usedJSHeapSize;
        }
    }

    registerCleanupCallback(callback) {
        this.cleanupCallbacks.push(callback);
    }

    async cleanup() {
        console.log('🧹 开始内存清理...');
        
        // 执行所有清理回调
        for (const callback of this.cleanupCallbacks) {
            try {
                await callback();
            } catch (error) {
                console.warn('清理回调失败:', error);
            }
        }

        // 手动触发垃圾回收（如果支持）
        if (window.gc) {
            window.gc();
        }

        this.stats.cleanupCount++;
        this.stats.lastCleanup = new Date().toISOString();
        
        console.log('✅ 内存清理完成');
    }

    getStats() {
        this.updateMemoryUsage();
        return { ...this.stats };
    }
}

// 预取管理器
class PrefetchManager {
    constructor(config) {
        this.enabled = config.prefetchEnabled || true;
        this.cache = new Map();
        this.maxCacheSize = 20;
        this.stats = {
            totalPrefetch: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
    }

    async prefetchQueries(queries, language) {
        if (!this.enabled) return;

        for (const query of queries) {
            try {
                const cacheKey = `${query}_${language}`;
                
                if (!this.cache.has(cacheKey)) {
                    // 模拟预取（实际应用中会调用真实的搜索API）
                    const result = await this.simulatePrefetch(query, language);
                    this.setCacheItem(cacheKey, result);
                    this.stats.totalPrefetch++;
                }
            } catch (error) {
                console.debug('预取查询失败:', query, error);
            }
        }
    }

    async simulatePrefetch(query, language) {
        // 模拟异步预取操作
        await new Promise(resolve => setTimeout(resolve, 100));
        return {
            query,
            language,
            timestamp: Date.now(),
            prefetched: true
        };
    }

    getCachedResult(query, language) {
        const cacheKey = `${query}_${language}`;
        const item = this.cache.get(cacheKey);
        
        if (item) {
            this.stats.cacheHits++;
            return item;
        } else {
            this.stats.cacheMisses++;
            return null;
        }
    }

    setCacheItem(key, value) {
        // LRU清理
        if (this.cache.size >= this.maxCacheSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        
        this.cache.set(key, value);
    }

    getStats() {
        return {
            ...this.stats,
            cacheSize: this.cache.size
        };
    }

    cleanup() {
        this.cache.clear();
    }
}

// 性能指标收集器
class PerformanceMetrics {
    constructor() {
        this.requests = [];
        this.maxHistory = 100;
    }

    recordRequest(status, duration, httpStatus, error = null) {
        this.requests.push({
            timestamp: Date.now(),
            status,
            duration,
            httpStatus,
            error
        });

        // 保持历史记录在限制范围内
        if (this.requests.length > this.maxHistory) {
            this.requests = this.requests.slice(-this.maxHistory);
        }
    }

    getStats() {
        const total = this.requests.length;
        const successful = this.requests.filter(r => r.status === 'success').length;
        const errors = total - successful;
        
        const durations = this.requests.map(r => r.duration);
        const avgDuration = durations.length > 0 ? 
            durations.reduce((a, b) => a + b, 0) / durations.length : 0;
        
        return {
            totalRequests: total,
            successfulRequests: successful,
            errorRequests: errors,
            successRate: total > 0 ? (successful / total * 100).toFixed(2) + '%' : '0%',
            avgResponseTime: Math.round(avgDuration) + 'ms',
            recentErrors: this.requests
                .filter(r => r.error)
                .slice(-5)
                .map(r => r.error)
        };
    }

    reset() {
        this.requests = [];
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceOptimizer;
} else {
    window.PerformanceOptimizer = PerformanceOptimizer;
}