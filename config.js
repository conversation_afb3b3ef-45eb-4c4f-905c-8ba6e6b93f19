// 环境配置文件
(function() {
    'use strict';
    
    // 创建配置对象
    const appConfig = {
        // Gemini API 配置
        gemini: {
            apiKey: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s', // 在这里替换为你的真实API Key
            model: 'gemini-2.5-flash-lite',
            endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/',
            enabled: true, // 默认启用
            temperature: 0.1,
            maxTokens: 1000
        },
        
        // 搜索配置
        search: {
            enableAI: true, // 默认启用AI搜索
            enableStreaming: true, // 启用流式搜索分析
            fallbackToTraditional: true, // 如果AI搜索失败，回退到传统搜索
            maxResults: 10,
            
            // 智能建议配置
            suggestions: {
                enabled: true, // 启用智能建议
                maxSuggestions: 8, // 最大建议数量
                maxHistory: 50, // 最大历史记录
                minSearchLength: 2, // 最小搜索长度
                showDefaultSuggestions: true, // 显示默认建议
                enableLearning: true // 启用学习功能
            },
            
            // 流式搜索特殊配置
            streaming: {
                enabled: true, // 流式搜索总开关
                debounceMs: 300, // 防抖延迟（从500ms优化到300ms）
                stages: {
                    basic: { timeoutMs: 2000 }, // 基础搜索超时
                    aiAnalysis: { timeoutMs: 5000 }, // AI分析超时
                    suggestions: { timeoutMs: 3000 }, // 建议生成超时
                    optimization: { timeoutMs: 4000 } // 结果优化超时
                },
                cache: {
                    maxSize: 50, // 缓存条目数
                    ttlMs: 300000 // 5分钟TTL
                },
                ui: {
                    animationDelay: 100, // 结果动画间隔（毫秒）
                    enableTouchFeedback: true, // 触摸反馈
                    showProgress: true, // 显示进度指示器
                    enableCancelGesture: true, // 支持手势取消
                    maxConcurrentRequests: 2 // 最大并发请求数
                }
            }
        },
        
        // 移动端配置
        mobile: {
            haptic: {
                enabled: true, // 启用触觉反馈
                patterns: {
                    selection: [10], // 选择反馈
                    impact: [50], // 冲击反馈
                    notification: [30, 50, 30] // 通知反馈
                }
            },
            gestures: {
                timeout: 300, // 手势超时时间
                longPress: {
                    timeout: 800, // 长按超时时间
                    enabled: true
                },
                swipe: {
                    threshold: 100, // 滑动阈值（像素）
                    enabled: true
                },
                doubleTap: {
                    timeout: 300, // 双击超时时间
                    enabled: true
                }
            },
            virtualKeyboard: {
                adaptUI: true, // 自动适配虚拟键盘
                hideNavigationOnOpen: true, // 键盘打开时隐藏底部导航
                threshold: 150, // 键盘检测阈值（像素）
                iosKeyboardFix: true, // iOS键盘修复
                androidKeyboardFix: true // Android键盘修复
            },
            safeArea: {
                enabled: true, // 启用安全区域支持
                adaptForNotch: true // 适配刘海屏
            },
            performance: {
                reducedMotion: false, // 减少动画（自动检测）
                touchOptimization: true, // 触摸优化
                disableUserZoom: false // 禁用用户缩放
            }
        },

        // 调试和测试配置
        debug: false, // 调试模式开关
        testing: {
            enabled: false, // 测试功能启用
            autoRunHealthCheck: false, // 自动运行健康检查
            performanceThresholds: {
                searchResponseTime: 2000,
                suggestionResponseTime: 500,
                uiRenderTime: 100,
                memoryUsageLimit: 100 * 1024 * 1024
            }
        }
    };

    // 浏览器环境下的配置处理
    if (typeof window !== 'undefined') {
        // 在客户端，我们从 localStorage 或默认值获取配置
        const savedConfig = localStorage.getItem('faq-config');
        if (savedConfig) {
            try {
                const parsed = JSON.parse(savedConfig);
                Object.assign(appConfig, parsed);
            } catch (e) {
                console.warn('Failed to parse saved config:', e);
            }
        }
        
        // 如果没有保存的API密钥，使用默认值（在生产环境中应该从安全的地方获取）
        if (!appConfig.gemini.apiKey || appConfig.gemini.apiKey.includes('XXXXX')) {
            // 这里应该从你的安全配置中获取API密钥
            // 例如从服务器端点获取，或者从环境变量注入的脚本标签中获取
            appConfig.gemini.apiKey = window.GEMINI_API_KEY || appConfig.gemini.apiKey;
        }
    }

    // 保存配置到 localStorage
    function saveConfig() {
        if (typeof window !== 'undefined') {
            localStorage.setItem('faq-config', JSON.stringify(appConfig));
        }
    }

    // 导出配置
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = appConfig;
    } else if (typeof window !== 'undefined') {
        window.CONFIG = appConfig;
        window.saveConfig = saveConfig;
    }

})();
