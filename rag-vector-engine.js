// RAG向量搜索引擎 - 基于语义相似度的内容检索 (性能优化版)
(function() {
    'use strict';

    // 性能监控器
    class RAGPerformanceMonitor {
        constructor() {
            this.metrics = {
                vectorizationTime: [],
                searchTime: [],
                cacheHits: 0,
                cacheMisses: 0,
                memoryUsage: [],
                workerTasks: 0
            };
        }

        recordVectorization(time) {
            this.metrics.vectorizationTime.push(time);
            if (this.metrics.vectorizationTime.length > 100) {
                this.metrics.vectorizationTime.shift();
            }
        }

        recordSearch(time) {
            this.metrics.searchTime.push(time);
            if (this.metrics.searchTime.length > 100) {
                this.metrics.searchTime.shift();
            }
        }

        recordCacheHit() { this.metrics.cacheHits++; }
        recordCacheMiss() { this.metrics.cacheMisses++; }

        getStats() {
            const avgVectorTime = this.getAverage(this.metrics.vectorizationTime);
            const avgSearchTime = this.getAverage(this.metrics.searchTime);
            const cacheHitRate = this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses) * 100;
            
            return {
                avgVectorizationTime: avgVectorTime,
                avgSearchTime: avgSearchTime,
                cacheHitRate: cacheHitRate || 0,
                totalCacheHits: this.metrics.cacheHits,
                totalCacheMisses: this.metrics.cacheMisses,
                workerTasks: this.metrics.workerTasks,
                memoryEstimate: this.estimateMemoryUsage()
            };
        }

        getAverage(arr) {
            return arr.length > 0 ? arr.reduce((a, b) => a + b, 0) / arr.length : 0;
        }

        estimateMemoryUsage() {
            if (performance && performance.memory) {
                return {
                    used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024 * 100) / 100,
                    total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024 * 100) / 100,
                    limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024 * 100) / 100
                };
            }
            return { used: 'N/A', total: 'N/A', limit: 'N/A' };
        }
    }

    // 向量计算Worker (Web Worker模拟)
    class VectorWorker {
        static computeCosineParallel(queryVector, documentVectors, chunkSize = 50) {
            return new Promise((resolve) => {
                const results = [];
                let processed = 0;
                
                const processChunk = () => {
                    const end = Math.min(processed + chunkSize, documentVectors.length);
                    
                    for (let i = processed; i < end; i++) {
                        const similarity = VectorSimilarity.cosineOptimized(queryVector, documentVectors[i]);
                        results.push({ index: i, similarity });
                    }
                    
                    processed = end;
                    
                    if (processed < documentVectors.length) {
                        // 使用 setTimeout 让浏览器处理其他任务
                        setTimeout(processChunk, 0);
                    } else {
                        resolve(results);
                    }
                };
                
                processChunk();
            });
        }
    }

    // 高性能TF-IDF向量化器 (优化版)
    class TFIDFVectorizer {
        constructor() {
            this.vocabulary = new Map();
            this.idf = new Map();
            this.documents = [];
            this.stopWords = new Set([
                // 中文停用词
                '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这',
                // 英文停用词  
                'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did',
                // 马来语停用词
                'yang', 'dan', 'di', 'ke', 'dari', 'untuk', 'dengan', 'pada', 'adalah', 'akan', 'telah', 'atau', 'ini', 'itu', 'juga', 'tidak', 'dapat', 'boleh'
            ]);
            
            // 性能优化缓存
            this.tokenCache = new Map(); // 最多缓存1000个token结果
            this.vectorCache = new Map(); // 向量化缓存
            this.maxCacheSize = 1000;
            
            // 预编译正则表达式
            this.chineseRegex = /[\u4e00-\u9fa5]+/g;
            this.latinRegex = /[a-zA-Z]+/g;
            this.numberRegex = /\d+/g;
            this.htmlTagRegex = /<[^>]*>/g;
            this.whitespaceRegex = /\s+/g;
        }

        // 高性能文本预处理和分词
        tokenize(text) {
            if (!text) return [];
            
            // 检查缓存
            const cacheKey = text.length < 200 ? text : text.substring(0, 200) + '...';
            if (this.tokenCache.has(cacheKey)) {
                return this.tokenCache.get(cacheKey);
            }
            
            // 移除HTML标签 - 优化版
            const cleanText = text.replace(this.htmlTagRegex, ' ');
            
            // 批量分词处理 - 避免多次字符串操作
            const tokens = [];
            
            // 中文分词
            let match;
            this.chineseRegex.lastIndex = 0;
            while ((match = this.chineseRegex.exec(cleanText)) !== null) {
                tokens.push(match[0]);
            }
            
            // 英文和马来语分词
            this.latinRegex.lastIndex = 0;
            while ((match = this.latinRegex.exec(cleanText)) !== null) {
                tokens.push(match[0]);
            }
            
            // 数字分词
            this.numberRegex.lastIndex = 0;
            while ((match = this.numberRegex.exec(cleanText)) !== null) {
                tokens.push(match[0]);
            }
            
            // 批量清理和标准化 - 单次遍历
            const result = [];
            for (let i = 0; i < tokens.length && result.length < 100; i++) {
                const token = tokens[i].toLowerCase().trim();
                if (token.length > 1 && !this.stopWords.has(token)) {
                    result.push(token);
                }
            }
            
            // 缓存结果
            if (this.tokenCache.size >= this.maxCacheSize) {
                const firstKey = this.tokenCache.keys().next().value;
                this.tokenCache.delete(firstKey);
            }
            this.tokenCache.set(cacheKey, result);
            
            return result;
        }

        // 高性能构建词汇表 - 批量处理和并行优化
        buildVocabulary(documents) {
            const startTime = Date.now();
            console.log('构建高性能词汇表...');
            this.documents = documents;
            
            // 批量分词 - 减少重复调用
            const documentTokens = documents.map(doc => {
                const tokens = this.tokenize(doc.text);
                return {
                    tokens,
                    uniqueTokens: new Set(tokens)
                };
            });
            
            // 统计词频 - 单次遍历
            const termFreq = new Map();
            const termDocFreq = new Map(); // 记录出现在多少文档中
            
            documentTokens.forEach(({ uniqueTokens }) => {
                uniqueTokens.forEach(token => {
                    termFreq.set(token, (termFreq.get(token) || 0) + 1);
                    termDocFreq.set(token, (termDocFreq.get(token) || 0) + 1);
                });
            });

            // 筛选词汇表 - 优化过滤条件
            const docCount = documents.length;
            const minFreq = Math.max(2, Math.floor(docCount * 0.01)); // 至少1%文档
            const maxFreq = Math.floor(docCount * 0.5); // 最多50%文档
            let vocabIndex = 0;
            
            termDocFreq.forEach((docFreq, term) => {
                if (docFreq >= minFreq && docFreq <= maxFreq) {
                    this.vocabulary.set(term, vocabIndex++);
                }
            });

            // 批量计算IDF - 避免重复分词
            this.vocabulary.forEach((index, term) => {
                const df = termDocFreq.get(term) || 0;
                this.idf.set(term, Math.log(docCount / (df + 1)));
            });

            const buildTime = Date.now() - startTime;
            console.log(`高性能词汇表构建完成: ${this.vocabulary.size} 个词汇, ${buildTime}ms`);
        }

        // 高性能文档向量化 - 缓存和批量处理
        vectorize(text, useCache = true) {
            if (useCache && this.vectorCache.has(text)) {
                return this.vectorCache.get(text);
            }
            
            const tokens = this.tokenize(text);
            const vocabSize = this.vocabulary.size;
            
            // 使用Float32Array提升性能和减少内存
            const vector = new Float32Array(vocabSize);
            const termCount = new Map();
            const tokensLength = tokens.length;

            // 单次遍历计算TF
            for (let i = 0; i < tokensLength; i++) {
                const token = tokens[i];
                termCount.set(token, (termCount.get(token) || 0) + 1);
            }

            // 批量计算TF-IDF - 避免重复查找
            termCount.forEach((tf, term) => {
                if (this.vocabulary.has(term)) {
                    const index = this.vocabulary.get(term);
                    const idf = this.idf.get(term) || 0;
                    vector[index] = (tf / tokensLength) * idf;
                }
            });
            
            // 向量归一化 - 提升相似度计算精度
            this.normalizeVector(vector);
            
            // 缓存结果
            if (useCache) {
                if (this.vectorCache.size >= this.maxCacheSize) {
                    const firstKey = this.vectorCache.keys().next().value;
                    this.vectorCache.delete(firstKey);
                }
                this.vectorCache.set(text, vector);
            }

            return vector;
        }
        
        // 向量归一化
        normalizeVector(vector) {
            let norm = 0;
            const length = vector.length;
            
            // 计算L2范数
            for (let i = 0; i < length; i++) {
                norm += vector[i] * vector[i];
            }
            
            norm = Math.sqrt(norm);
            if (norm > 0) {
                for (let i = 0; i < length; i++) {
                    vector[i] /= norm;
                }
            }
        }
    }

    // 高性能向量相似度计算器
    class VectorSimilarity {
        // 优化的余弦相似度计算
        static cosine(vecA, vecB) {
            if (vecA.length !== vecB.length) return 0;

            let dotProduct = 0;
            let normA = 0;
            let normB = 0;
            const length = vecA.length;

            // 单次遍历计算所有值
            for (let i = 0; i < length; i++) {
                const a = vecA[i];
                const b = vecB[i];
                dotProduct += a * b;
                normA += a * a;
                normB += b * b;
            }

            const norm = Math.sqrt(normA * normB);
            return norm > 0 ? dotProduct / norm : 0;
        }
        
        // 专门为归一化向量优化的余弦相似度 (更快) - 修复版
        static cosineOptimized(vecA, vecB) {
            if (vecA.length !== vecB.length) return 0;
            
            let dotProduct = 0;
            let normA = 0;
            let normB = 0;
            const length = vecA.length;
            
            // 计算点积和范数（确保正确性）
            for (let i = 0; i < length; i++) {
                const a = vecA[i];
                const b = vecB[i];
                dotProduct += a * b;
                normA += a * a;
                normB += b * b;
            }
            
            // 计算余弦相似度
            const norm = Math.sqrt(normA * normB);
            const similarity = norm > 0 ? dotProduct / norm : 0;
            
            // 确保相似度在合理范围内
            return Math.max(0, Math.min(1, similarity));
        }
        
        // SIMD优化的向量相似度 (在支持的浏览器中)
        static cosineSimd(vecA, vecB) {
            // 检查是否支持SIMD
            if (typeof SIMD === 'undefined' || SIMD.Float32x4 === 'undefined') {
                return VectorSimilarity.cosineOptimized(vecA, vecB);
            }
            
            if (vecA.length !== vecB.length) return 0;
            
            const length = vecA.length;
            const simdLength = Math.floor(length / 4) * 4;
            let dotProduct = 0;
            
            // SIMD处理4个元素为一组
            for (let i = 0; i < simdLength; i += 4) {
                const a = SIMD.Float32x4.load(vecA, i);
                const b = SIMD.Float32x4.load(vecB, i);
                const product = SIMD.Float32x4.mul(a, b);
                dotProduct += SIMD.Float32x4.extractLane(product, 0);
                dotProduct += SIMD.Float32x4.extractLane(product, 1);
                dotProduct += SIMD.Float32x4.extractLane(product, 2);
                dotProduct += SIMD.Float32x4.extractLane(product, 3);
            }
            
            // 处理剩余元素
            for (let i = simdLength; i < length; i++) {
                dotProduct += vecA[i] * vecB[i];
            }
            
            return dotProduct;
        }

        // 欧几里得距离
        static euclidean(vecA, vecB) {
            if (vecA.length !== vecB.length) return Infinity;

            let sum = 0;
            for (let i = 0; i < vecA.length; i++) {
                sum += Math.pow(vecA[i] - vecB[i], 2);
            }
            return Math.sqrt(sum);
        }

        // 曼哈顿距离
        static manhattan(vecA, vecB) {
            if (vecA.length !== vecB.length) return Infinity;

            let sum = 0;
            for (let i = 0; i < vecA.length; i++) {
                sum += Math.abs(vecA[i] - vecB[i]);
            }
            return sum;
        }
    }

    // RAG向量搜索引擎主类
    class RAGVectorEngine {
        constructor(options = {}) {
            this.options = {
                maxResults: options.maxResults || 10,
                similarityThreshold: options.similarityThreshold || 0.1,
                enableCache: options.enableCache !== false,
                cacheSize: options.cacheSize || 100,
                enableParallelProcessing: options.enableParallelProcessing !== false,
                chunkSize: options.chunkSize || 50,
                enableMemoryOptimization: options.enableMemoryOptimization !== false,
                vectorCompressionLevel: options.vectorCompressionLevel || 0.1,
                ...options
            };

            this.vectorizer = new TFIDFVectorizer();
            this.documents = [];
            this.documentVectors = [];
            this.isInitialized = false;
            
            // 高性能缓存系统
            this.cache = new Map();
            this.cacheKeys = [];
            this.compressedCache = new Map(); // 压缩缓存
            
            // 性能监控
            this.performanceMonitor = new RAGPerformanceMonitor();
            
            // 内存优化
            this.enableMemoryCompaction = this.options.enableMemoryOptimization;
            
            console.log('高性能RAG向量搜索引擎初始化');
            console.log('配置:', this.options);
        }

        // 从FAQ数据初始化
        async initializeFromFAQData(faqData) {
            console.log('从FAQ数据初始化向量引擎...');
            const startTime = Date.now();

            try {
                // 提取文档
                this.documents = faqData.questions.map(question => {
                    // 合并多语言内容进行向量化
                    const combinedText = [
                        question.title.zh || '',
                        question.title.en || '',
                        question.title.ms || '',
                        this.extractTextFromHTML(question.content.zh || ''),
                        this.extractTextFromHTML(question.content.en || ''),
                        this.extractTextFromHTML(question.content.ms || ''),
                        (question.tags || []).join(' ')
                    ].join(' ');

                    return {
                        id: question.id,
                        text: combinedText,
                        question: question,
                        // 主要语言内容用于显示
                        primaryTitle: question.title.zh || question.title.en || question.title.ms,
                        primaryContent: question.content.zh || question.content.en || question.content.ms,
                        category: question.category,
                        priority: question.priority,
                        tags: question.tags || []
                    };
                });

                // 构建词汇表
                this.vectorizer.buildVocabulary(this.documents);

                // 并行向量化所有文档 - 性能优化
                console.log('并行向量化文档...');
                const vectorizationStart = Date.now();
                
                if (this.options.enableParallelProcessing && this.documents.length > 20) {
                    this.documentVectors = await this.parallelVectorization(this.documents);
                } else {
                    this.documentVectors = this.documents.map(doc => 
                        this.vectorizer.vectorize(doc.text)
                    );
                }
                
                const vectorizationTime = Date.now() - vectorizationStart;
                this.performanceMonitor.recordVectorization(vectorizationTime);
                
                // 内存优化 - 向量压缩
                if (this.enableMemoryCompaction) {
                    this.compactVectors();
                }

                this.isInitialized = true;
                const initTime = Date.now() - startTime;
                console.log(`RAG向量引擎初始化完成: ${this.documents.length} 个文档, ${initTime}ms`);

                return {
                    success: true,
                    documentsCount: this.documents.length,
                    vocabularySize: this.vectorizer.vocabulary.size,
                    initTime
                };

            } catch (error) {
                console.error('RAG向量引擎初始化失败:', error);
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        // 从HTML中提取纯文本
        extractTextFromHTML(html) {
            if (typeof document !== 'undefined') {
                const div = document.createElement('div');
                div.innerHTML = html;
                return div.textContent || div.innerText || '';
            }
            // Fallback for non-browser environments
            return html.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
        }

        // 高性能语义搜索
        async semanticSearch(query, options = {}) {
            if (!this.isInitialized) {
                throw new Error('RAG向量引擎未初始化');
            }

            const startTime = Date.now();
            const searchOptions = { ...this.options, ...options };

            // 检查缓存
            const cacheKey = `${query}:${JSON.stringify(searchOptions)}`;
            if (this.options.enableCache && this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                this.performanceMonitor.recordCacheHit();
                console.debug('RAG缓存命中:', query);
                return {
                    ...cached,
                    cached: true,
                    searchTime: Date.now() - startTime
                };
            }
            
            this.performanceMonitor.recordCacheMiss();

            try {
                // 高性能查询向量化 - 不缓存查询向量
                const queryVector = this.vectorizer.vectorize(query, false);

                // 并行相似度计算
                let similarities;
                if (this.options.enableParallelProcessing && this.documentVectors.length > 50) {
                    similarities = await this.parallelSimilarityCalculation(queryVector);
                } else {
                    similarities = this.documentVectors.map((docVector, index) => ({
                        index,
                        similarity: VectorSimilarity.cosineOptimized(queryVector, docVector),
                        document: this.documents[index]
                    }));
                }

                // 优化的筛选和排序 - Top-K算法
                const filteredResults = this.topKSimilarities(
                    similarities, 
                    searchOptions.similarityThreshold, 
                    searchOptions.maxResults
                );

                // 格式化结果
                const results = filteredResults.map(result => ({
                    id: result.document.id,
                    question: result.document.question,
                    similarity: result.similarity,
                    score: result.similarity, // 兼容现有系统
                    matchType: 'semantic',
                    title: result.document.question.title,
                    content: result.document.question.content,
                    category: result.document.category,
                    priority: result.document.priority,
                    tags: result.document.tags,
                    vectorMatch: true
                }));

                const searchTime = Date.now() - startTime;
                this.performanceMonitor.recordSearch(searchTime);
                
                const searchResult = {
                    results,
                    query,
                    totalMatches: filteredResults.length,
                    searchTime,
                    method: 'semantic_vector_optimized',
                    cached: false,
                    performance: {
                        vectorizationOptimized: true,
                        parallelProcessing: this.options.enableParallelProcessing && this.documentVectors.length > 50,
                        memoryOptimized: this.enableMemoryCompaction
                    }
                };

                // 智能缓存结果 - 压缩大结果
                if (this.options.enableCache) {
                    if (results.length > 5) {
                        this.addToCompressedCache(cacheKey, searchResult);
                    } else {
                        this.addToCache(cacheKey, searchResult);
                    }
                }

                console.debug(`RAG高性能语义搜索: "${query}" -> ${results.length} 个结果, ${searchResult.searchTime}ms`);
                return searchResult;

            } catch (error) {
                console.error('RAG语义搜索失败:', error);
                return {
                    results: [],
                    query,
                    error: error.message,
                    searchTime: Date.now() - startTime
                };
            }
        }

        // 混合搜索 (向量 + 传统)
        async hybridSearch(query, traditionalResults = [], options = {}) {
            const vectorResults = await this.semanticSearch(query, options);
            
            if (!vectorResults.results || vectorResults.results.length === 0) {
                return traditionalResults;
            }

            // 合并结果，去重
            const existingIds = new Set(traditionalResults.map(r => r.id));
            const newVectorResults = vectorResults.results.filter(r => !existingIds.has(r.id));

            // 重新排序：向量搜索结果在前，传统搜索结果在后
            const hybridResults = [
                ...vectorResults.results.map(r => ({ ...r, source: 'vector' })),
                ...traditionalResults.map(r => ({ ...r, source: 'traditional' }))
            ];

            return {
                results: hybridResults,
                totalMatches: hybridResults.length,
                vectorMatches: vectorResults.results.length,
                traditionalMatches: traditionalResults.length,
                method: 'hybrid'
            };
        }

        // 相似内容推荐
        async findSimilar(questionId, options = {}) {
            if (!this.isInitialized) return [];

            const docIndex = this.documents.findIndex(doc => doc.id === questionId);
            if (docIndex === -1) return [];

            const targetVector = this.documentVectors[docIndex];
            const similarities = this.documentVectors
                .map((vector, index) => ({
                    index,
                    similarity: VectorSimilarity.cosine(targetVector, vector),
                    document: this.documents[index]
                }))
                .filter(result => result.index !== docIndex && result.similarity > 0.3)
                .sort((a, b) => b.similarity - a.similarity)
                .slice(0, options.maxResults || 5);

            return similarities.map(result => ({
                id: result.document.id,
                question: result.document.question,
                similarity: result.similarity,
                matchType: 'similar_content'
            }));
        }

        // 缓存管理
        addToCache(key, value) {
            if (this.cache.size >= this.options.cacheSize) {
                // LRU: 删除最旧的缓存
                const oldestKey = this.cacheKeys.shift();
                this.cache.delete(oldestKey);
            }
            
            this.cache.set(key, value);
            this.cacheKeys.push(key);
        }

        clearCache() {
            this.cache.clear();
            this.cacheKeys = [];
            this.compressedCache.clear();
            
            // 清理向量化器缓存
            if (this.vectorizer) {
                this.vectorizer.tokenCache.clear();
                this.vectorizer.vectorCache.clear();
            }
            
            console.log('RAG所有缓存已清理');
        }

        // 获取详细性能统计信息
        getStats() {
            const perfStats = this.performanceMonitor.getStats();
            return {
                initialized: this.isInitialized,
                documentsCount: this.documents.length,
                vocabularySize: this.vectorizer.vocabulary.size,
                cacheSize: this.cache.size,
                compressedCacheSize: this.compressedCache.size,
                cacheHitRate: perfStats.cacheHitRate,
                performance: {
                    avgVectorizationTime: perfStats.avgVectorizationTime,
                    avgSearchTime: perfStats.avgSearchTime,
                    memoryUsage: perfStats.memoryEstimate,
                    tokenCacheSize: this.vectorizer.tokenCache.size,
                    vectorCacheSize: this.vectorizer.vectorCache.size
                },
                optimization: {
                    parallelProcessing: this.options.enableParallelProcessing,
                    memoryOptimization: this.enableMemoryCompaction,
                    vectorCompression: this.options.vectorCompressionLevel > 0
                }
            };
        }

        // 导出向量数据(可选，用于持久化)
        exportVectorData() {
            if (!this.isInitialized) return null;

            return {
                documents: this.documents.map(doc => ({
                    id: doc.id,
                    text: doc.text.substring(0, 100) // 只导出前100字符作为摘要
                })),
                vocabulary: Array.from(this.vectorizer.vocabulary.entries()),
                vectors: this.documentVectors.map(vector => 
                    vector.map(v => Math.round(v * 10000) / 10000) // 保留4位小数
                )
            };
        }

        // 并行向量化处理
        async parallelVectorization(documents) {
            console.log(`并行向量化处理 ${documents.length} 个文档...`);
            const vectors = [];
            const chunkSize = this.options.chunkSize;
            
            for (let i = 0; i < documents.length; i += chunkSize) {
                const chunk = documents.slice(i, i + chunkSize);
                const chunkVectors = await new Promise(resolve => {
                    setTimeout(() => {
                        const result = chunk.map(doc => this.vectorizer.vectorize(doc.text));
                        resolve(result);
                    }, 0);
                });
                vectors.push(...chunkVectors);
                
                // 进度报告
                if (i % (chunkSize * 4) === 0) {
                    console.log(`向量化进度: ${Math.min(i + chunkSize, documents.length)}/${documents.length}`);
                }
            }
            
            console.log('并行向量化完成');
            return vectors;
        }
        
        // 并行相似度计算
        async parallelSimilarityCalculation(queryVector) {
            console.debug('使用并行相似度计算...');
            const similarities = await VectorWorker.computeCosineParallel(
                queryVector, 
                this.documentVectors, 
                this.options.chunkSize
            );
            
            return similarities.map(sim => ({
                index: sim.index,
                similarity: sim.similarity,
                document: this.documents[sim.index]
            }));
        }
        
        // Top-K相似度筛选算法 - 避免全排序 (优化版)
        topKSimilarities(similarities, threshold, k) {
            const validSimilarities = [];
            
            // 第一次过滤 - 只保留超过阈值的
            for (let i = 0; i < similarities.length; i++) {
                if (similarities[i].similarity >= threshold) {
                    validSimilarities.push(similarities[i]);
                }
            }
            
            // 调试日志：检查过滤情况
            if (validSimilarities.length === 0) {
                console.debug(`📊 RAG搜索调试: 阈值${threshold}过滤后0个结果`);
                // 如果严格阈值没有结果，降低阈值再试一次
                const relaxedThreshold = Math.max(0.01, threshold * 0.3);
                console.debug(`📊 RAG搜索调试: 尝试放宽阈值到${relaxedThreshold}`);
                
                for (let i = 0; i < similarities.length; i++) {
                    if (similarities[i].similarity >= relaxedThreshold) {
                        validSimilarities.push(similarities[i]);
                    }
                }
                
                // 如果还是没有结果，至少返回相似度最高的几个
                if (validSimilarities.length === 0 && similarities.length > 0) {
                    console.debug(`📊 RAG搜索调试: 返回Top3最相似结果作为fallback`);
                    const sortedAll = similarities.slice().sort((a, b) => b.similarity - a.similarity);
                    validSimilarities.push(...sortedAll.slice(0, Math.min(3, sortedAll.length)));
                }
            }
            
            // 如果结果不多，直接排序
            if (validSimilarities.length <= k * 2) {
                return validSimilarities
                    .sort((a, b) => b.similarity - a.similarity)
                    .slice(0, k);
            }
            
            // 使用快速选择算法找Top-K
            return this.quickSelectTopK(validSimilarities, k);
        }
        
        // 快速选择Top-K算法
        quickSelectTopK(arr, k) {
            if (arr.length <= k) return arr.sort((a, b) => b.similarity - a.similarity);
            
            const pivot = arr[Math.floor(Math.random() * arr.length)].similarity;
            const higher = arr.filter(x => x.similarity > pivot);
            const equal = arr.filter(x => x.similarity === pivot);
            const lower = arr.filter(x => x.similarity < pivot);
            
            if (higher.length >= k) {
                return this.quickSelectTopK(higher, k);
            } else if (higher.length + equal.length >= k) {
                return [...higher, ...equal.slice(0, k - higher.length)]
                    .sort((a, b) => b.similarity - a.similarity);
            } else {
                return [...higher, ...equal, ...this.quickSelectTopK(lower, k - higher.length - equal.length)]
                    .sort((a, b) => b.similarity - a.similarity);
            }
        }
        
        // 向量压缩 - 减少内存使用
        compactVectors() {
            console.log('执行向量内存压缩...');
            const threshold = this.options.vectorCompressionLevel;
            
            this.documentVectors = this.documentVectors.map(vector => {
                // 将非常小的值设为0以提升稀疏性
                const compressedVector = new Float32Array(vector.length);
                for (let i = 0; i < vector.length; i++) {
                    compressedVector[i] = Math.abs(vector[i]) < threshold ? 0 : vector[i];
                }
                return compressedVector;
            });
            
            console.log('向量压缩完成');
        }
        
        // 压缩缓存 - 大结果使用压缩存储
        addToCompressedCache(key, value) {
            try {
                // 简单压缩：只保留重要信息
                const compressed = {
                    results: value.results.map(r => ({
                        id: r.id,
                        similarity: Math.round(r.similarity * 10000) / 10000, // 保留4位小数
                        matchType: r.matchType
                    })),
                    totalMatches: value.totalMatches,
                    searchTime: value.searchTime,
                    method: value.method,
                    compressed: true
                };
                
                if (this.compressedCache.size >= Math.floor(this.options.cacheSize / 2)) {
                    const firstKey = this.compressedCache.keys().next().value;
                    this.compressedCache.delete(firstKey);
                }
                
                this.compressedCache.set(key, compressed);
            } catch (error) {
                console.warn('压缩缓存失败，使用普通缓存:', error);
                this.addToCache(key, value);
            }
        }
        
        // 重建索引（用于数据更新）- 支持增量更新
        async rebuildIndex(faqData, incremental = false) {
            console.log(incremental ? '增量重建RAG向量索引...' : '完全重建RAG向量索引...');
            
            if (!incremental) {
                this.clearCache();
                return this.initializeFromFAQData(faqData);
            }
            
            // 增量更新逻辑 - 只处理新增或修改的文档
            console.log('增量更新功能开发中...');
            return this.initializeFromFAQData(faqData);
        }
        
        // 性能基准测试
        async benchmark(testQueries = ['payment', 'login', 'crash', 'support']) {
            if (!this.isInitialized) {
                throw new Error('引擎未初始化');
            }
            
            console.log('开始RAG性能基准测试...');
            const results = {};
            
            for (const query of testQueries) {
                const iterations = 10;
                const times = [];
                
                for (let i = 0; i < iterations; i++) {
                    const start = Date.now();
                    await this.semanticSearch(query, { maxResults: 5 });
                    times.push(Date.now() - start);
                }
                
                results[query] = {
                    avg: times.reduce((a, b) => a + b, 0) / iterations,
                    min: Math.min(...times),
                    max: Math.max(...times),
                    iterations
                };
            }
            
            const stats = this.getStats();
            console.log('RAG基准测试完成:', results);
            console.log('当前性能统计:', stats);
            
            return { benchmarks: results, currentStats: stats };
        }
    }

    // 导出到全局
    if (typeof window !== 'undefined') {
        window.RAGVectorEngine = RAGVectorEngine;
        window.VectorSimilarity = VectorSimilarity;
        window.TFIDFVectorizer = TFIDFVectorizer;
        window.RAGPerformanceMonitor = RAGPerformanceMonitor;
        window.VectorWorker = VectorWorker;
    }
    
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = { 
            RAGVectorEngine, 
            VectorSimilarity, 
            TFIDFVectorizer, 
            RAGPerformanceMonitor, 
            VectorWorker 
        };
    }

})();