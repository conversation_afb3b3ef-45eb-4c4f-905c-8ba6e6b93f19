/* 统一搜索引擎样式 - 支持渐进式搜索结果显示 */

/* 搜索进度指示器 */
.search-progress {
    margin: 1rem 0;
    padding: 0.75rem;
    background: rgba(124, 58, 237, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(124, 58, 237, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #7c3aed, #a855f7);
    border-radius: 2px;
    transition: width 0.5s ease;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.progress-text {
    font-size: 0.875rem;
    color: #7c3aed;
    font-weight: 500;
}

/* 搜索结果项样式 */
.search-result-item {
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 1.25rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.search-result-item:hover {
    border-color: #7c3aed;
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.1);
    transform: translateY(-2px);
}

.search-result-item.high-confidence {
    border-left: 4px solid #10b981;
}

.search-result-item.medium-confidence {
    border-left: 4px solid #f59e0b;
}

.search-result-item.low-confidence {
    border-left: 4px solid #ef4444;
}

.search-result-item.ai-enhanced {
    background: linear-gradient(135deg, #fff, #faf5ff);
    border-color: #a855f7;
}

.search-result-item.match-exact::before {
    content: '🎯';
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.2rem;
}

.search-result-item.priority-high {
    background: linear-gradient(135deg, #fff, #fef3f2);
}

/* 搜索结果头部 */
.search-result-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

.search-result-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    line-height: 1.4;
    flex: 1;
    margin-right: 1rem;
}

.search-result-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    font-size: 0.75rem;
    color: #6b7280;
    white-space: nowrap;
}

.result-id {
    color: #7c3aed;
    font-weight: 500;
}

.result-score {
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 12px;
    margin: 2px 0;
}

.result-category {
    background: #ddd6fe;
    color: #5b21b6;
    padding: 2px 6px;
    border-radius: 12px;
}

/* 搜索结果内容 */
.search-result-content {
    margin-bottom: 1rem;
    line-height: 1.6;
    color: #4b5563;
}

.search-result-content p {
    margin: 0;
}

/* 查询高亮 */
.query-highlight {
    background: #fef3c7;
    color: #92400e;
    padding: 1px 3px;
    border-radius: 3px;
    font-weight: 500;
}

/* 结果标识 */
.result-badge {
    display: inline-block;
    font-size: 0.875rem;
    margin-left: 0.25rem;
    vertical-align: middle;
}

.badge-cached { color: #10b981; }
.badge-ai { color: #7c3aed; }
.badge-priority { color: #ef4444; }
.badge-exact { color: #059669; }

/* 搜索结果操作 */
.search-result-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.btn-view-details {
    background: #7c3aed;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-view-details:hover {
    background: #6d28d9;
    transform: translateY(-1px);
}

.related-count {
    font-size: 0.75rem;
    color: #6b7280;
}

/* 加载指示器 */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #7c3aed;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #7c3aed;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.75rem;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-text {
    font-weight: 500;
}

/* AI增强通知 */
.ai-enhancement-notice {
    display: flex;
    align-items: center;
    background: linear-gradient(90deg, #a855f7, #7c3aed);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.ai-icon {
    font-size: 1.2rem;
    margin-right: 0.5rem;
}

.ai-enhancement-notice small {
    margin-left: 0.5rem;
    opacity: 0.8;
    font-weight: 400;
}

/* 无结果显示 */
.no-results {
    text-align: center;
    padding: 3rem 1rem;
    color: #6b7280;
}

.no-results-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.no-results-message {
    font-size: 1.125rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* 搜索错误显示 */
.search-error {
    text-align: center;
    padding: 3rem 1rem;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 12px;
    margin: 1rem 0;
}

.error-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.error-message {
    font-size: 1.125rem;
    color: #ef4444;
    margin-bottom: 1.5rem;
}

.btn-retry {
    background: #ef4444;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-retry:hover {
    background: #dc2626;
}

/* 动画效果 */
.animate-fadeIn {
    animation: fadeIn 0.5s ease;
}

.animate-slideIn {
    animation: slideIn 0.5s ease;
}

.animate-in {
    animation: slideInUp 0.4s ease;
}

.updated {
    animation: pulse 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { 
        transform: translateX(-20px);
        opacity: 0;
    }
    to { 
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* 搜索统计 */
.search-stats {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.5rem;
    font-style: italic;
}

.search-results-header {
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-result-item {
        padding: 1rem;
        margin-bottom: 0.75rem;
    }
    
    .search-result-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .search-result-meta {
        flex-direction: row;
        align-items: center;
        margin-top: 0.5rem;
    }
    
    .search-result-meta > * {
        margin-right: 0.75rem;
    }
    
    .search-result-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }
    
    .btn-view-details {
        width: 100%;
    }
    
    .progress-text {
        font-size: 0.8rem;
    }
}

/* 暗色主题支持 */
[data-theme="dark"] .search-result-item {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
}

[data-theme="dark"] .search-result-item:hover {
    border-color: #a855f7;
    background: #111827;
}

[data-theme="dark"] .search-result-title {
    color: #f9fafb;
}

[data-theme="dark"] .search-result-content {
    color: #d1d5db;
}

[data-theme="dark"] .query-highlight {
    background: #451a03;
    color: #fbbf24;
}

[data-theme="dark"] .progress-bar {
    background: rgba(156, 163, 175, 0.2);
}

[data-theme="dark"] .search-progress {
    background: rgba(124, 58, 237, 0.15);
}

[data-theme="dark"] .no-results,
[data-theme="dark"] .search-error {
    background: #111827;
    border-color: #374151;
    color: #d1d5db;
}

/* 性能优化 */
.search-result-item {
    will-change: transform;
}

.search-result-item:hover {
    will-change: transform, box-shadow;
}

/* 确保动画性能 */
@media (prefers-reduced-motion: reduce) {
    .search-result-item,
    .progress-fill,
    .loading-spinner,
    .animate-fadeIn,
    .animate-slideIn,
    .animate-in,
    .updated {
        animation: none;
        transition: none;
    }
}