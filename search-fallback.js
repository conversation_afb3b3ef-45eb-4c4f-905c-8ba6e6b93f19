// 搜索降级策略管理器
// 实现四层降级：流式AI → 标准AI → 模糊匹配 → 基础搜索

class SearchFallbackManager {
    constructor(geminiAssistant, streamingEngine, dataManager, i18n) {
        this.geminiAssistant = geminiAssistant;
        this.streamingEngine = streamingEngine;
        this.dataManager = dataManager;
        this.i18n = i18n;
        
        // 降级策略配置
        this.strategies = [
            'streaming-ai',
            'standard-ai', 
            'fuzzy-match',
            'basic-search'
        ];
        
        // 错误统计
        this.errorStats = {
            'streaming-ai': 0,
            'standard-ai': 0,
            'fuzzy-match': 0,
            'basic-search': 0
        };
        
        // 成功统计
        this.successStats = {
            'streaming-ai': 0,
            'standard-ai': 0,
            'fuzzy-match': 0,
            'basic-search': 0
        };
    }

    // 主搜索入口 - 按策略顺序尝试
    async performSearchWithFallback(query, language, onProgress = null, onComplete = null) {
        console.log(`🔄 开始降级搜索策略: "${query}"`);
        
        let lastError = null;
        let strategyIndex = 0;
        
        for (const strategy of this.strategies) {
            try {
                console.log(`📍 尝试策略 ${strategyIndex + 1}/4: ${strategy}`);
                
                // 更新进度（如果有回调）
                if (onProgress) {
                    onProgress({
                        strategy: strategy,
                        stage: 'trying',
                        attempt: strategyIndex + 1,
                        total: this.strategies.length
                    });
                }

                const result = await this.executeStrategy(strategy, query, language, onProgress);
                
                if (this.isValidResult(result)) {
                    console.log(`✅ 策略 ${strategy} 成功，找到 ${result.results?.length || 0} 个结果`);
                    
                    // 记录成功
                    this.recordSuccess(strategy);
                    
                    // 标记使用的策略
                    result.strategy = strategy;
                    result.fallbackLevel = strategyIndex;
                    
                    if (onComplete) {
                        onComplete(result);
                    }
                    
                    return result;
                }

            } catch (error) {
                console.warn(`⚠️ 策略 ${strategy} 失败:`, error.message);
                
                // 记录错误
                this.recordError(strategy, error);
                lastError = error;
                
                // 如果不是最后一个策略，继续下一个
                if (strategyIndex < this.strategies.length - 1) {
                    console.log(`⬇️ 降级到下一策略...`);
                    continue;
                } else {
                    console.error(`❌ 所有搜索策略都失败了`);
                }
            }
            
            strategyIndex++;
        }
        
        // 所有策略都失败，返回空结果
        const emptyResult = this.createEmptyResult(query, language, lastError);
        
        if (onComplete) {
            onComplete(emptyResult);
        }
        
        return emptyResult;
    }

    // 执行特定搜索策略
    async executeStrategy(strategy, query, language, onProgress = null) {
        const timeout = this.getStrategyTimeout(strategy);
        
        return new Promise(async (resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error(`策略 ${strategy} 超时 (${timeout}ms)`));
            }, timeout);
            
            try {
                let result;
                
                switch (strategy) {
                    case 'streaming-ai':
                        result = await this.executeStreamingAI(query, language, onProgress);
                        break;
                        
                    case 'standard-ai':
                        result = await this.executeStandardAI(query, language);
                        break;
                        
                    case 'fuzzy-match':
                        result = await this.executeFuzzyMatch(query, language);
                        break;
                        
                    case 'basic-search':
                        result = await this.executeBasicSearch(query, language);
                        break;
                        
                    default:
                        throw new Error(`未知搜索策略: ${strategy}`);
                }
                
                clearTimeout(timeoutId);
                resolve(result);
                
            } catch (error) {
                clearTimeout(timeoutId);
                reject(error);
            }
        });
    }

    // 策略1: 流式AI搜索
    async executeStreamingAI(query, language, onProgress) {
        if (!this.streamingEngine) {
            throw new Error('StreamingEngine未初始化');
        }
        
        return new Promise((resolve, reject) => {
            this.streamingEngine.performStreamingAnalysis(
                query,
                language,
                // 进度回调
                (progress) => {
                    if (onProgress) {
                        onProgress({
                            strategy: 'streaming-ai',
                            stage: progress.stage,
                            data: progress
                        });
                    }
                },
                // 完成回调
                (finalResult) => {
                    if (finalResult && !finalResult.error) {
                        resolve(finalResult);
                    } else {
                        reject(new Error('流式搜索失败或无结果'));
                    }
                }
            );
        });
    }

    // 策略2: 标准AI搜索
    async executeStandardAI(query, language) {
        if (!this.geminiAssistant || !this.geminiAssistant.isAvailable()) {
            throw new Error('Gemini助手不可用');
        }

        // 使用传统的AI增强搜索
        const enhanced = await this.geminiAssistant.enhanceSearchQuery(query, language);
        
        if (enhanced.enhanced && enhanced.enhancedQuery) {
            const results = await this.dataManager.searchQuestions(enhanced.enhancedQuery, language);
            
            return {
                query: query,
                language: language,
                results: results,
                enhanced: true,
                enhancedQuery: enhanced.enhancedQuery,
                keywords: enhanced.keywords,
                suggestions: enhanced.suggestions
            };
        } else {
            throw new Error('AI增强搜索失败');
        }
    }

    // 策略3: 模糊匹配搜索
    async executeFuzzyMatch(query, language) {
        console.log('🔍 执行模糊匹配搜索');
        
        // 扩展搜索词
        const expandedQueries = this.generateFuzzyQueries(query, language);
        let allResults = [];
        
        for (const expandedQuery of expandedQueries) {
            try {
                const results = await this.dataManager.searchQuestions(expandedQuery, language);
                allResults = allResults.concat(results);
            } catch (error) {
                console.debug('模糊搜索词失败:', expandedQuery, error);
            }
        }
        
        // 去重和排序
        const uniqueResults = this.deduplicateResults(allResults);
        const sortedResults = this.sortResultsByRelevance(uniqueResults, query);
        
        if (sortedResults.length === 0) {
            throw new Error('模糊匹配无结果');
        }
        
        return {
            query: query,
            language: language,
            results: sortedResults,
            enhanced: false,
            fuzzyQueries: expandedQueries
        };
    }

    // 策略4: 基础搜索
    async executeBasicSearch(query, language) {
        console.log('🔍 执行基础搜索');
        
        const results = await this.dataManager.searchQuestions(query, language);
        
        if (results.length === 0) {
            throw new Error('基础搜索无结果');
        }
        
        return {
            query: query,
            language: language,
            results: results,
            enhanced: false
        };
    }

    // 生成模糊搜索查询
    generateFuzzyQueries(query, language) {
        const queries = [query]; // 原始查询
        const queryLower = query.toLowerCase().trim();
        
        // 1. 去掉标点符号
        const noPunctuation = queryLower.replace(/[^\w\s\u4e00-\u9fff]/g, ' ').trim();
        if (noPunctuation !== queryLower && noPunctuation.length > 0) {
            queries.push(noPunctuation);
        }
        
        // 2. 分词搜索（中文按字符，英文按单词）
        if (language === 'zh') {
            // 中文：提取2-3字的关键词组合
            const chars = queryLower.replace(/\s+/g, '');
            for (let i = 0; i <= chars.length - 2; i++) {
                const chunk = chars.substring(i, i + 2);
                if (chunk.length === 2 && !queries.includes(chunk)) {
                    queries.push(chunk);
                }
            }
        } else {
            // 英文：单词分解
            const words = queryLower.split(/\s+/).filter(word => word.length > 2);
            words.forEach(word => {
                if (!queries.includes(word)) {
                    queries.push(word);
                }
            });
        }
        
        // 3. 常见拼写变体（仅英文）
        if (language === 'en') {
            const variants = this.generateSpellingVariants(queryLower);
            variants.forEach(variant => {
                if (!queries.includes(variant)) {
                    queries.push(variant);
                }
            });
        }
        
        // 限制查询数量避免性能问题
        return queries.slice(0, 5);
    }

    // 生成拼写变体（简单版本）
    generateSpellingVariants(query) {
        const variants = [];
        
        // 常见英文拼写错误模式
        const patterns = [
            [/login/g, 'log in'],
            [/log in/g, 'login'],
            [/signup/g, 'sign up'],
            [/sign up/g, 'signup'],
            [/withdraw/g, 'withdrawal'],
            [/withdrawal/g, 'withdraw']
        ];
        
        patterns.forEach(([pattern, replacement]) => {
            if (pattern.test(query)) {
                variants.push(query.replace(pattern, replacement));
            }
        });
        
        return variants;
    }

    // 去重结果
    deduplicateResults(results) {
        const seenIds = new Set();
        return results.filter(result => {
            const id = result.id || result.question?.id;
            if (seenIds.has(id)) {
                return false;
            }
            seenIds.add(id);
            return true;
        });
    }

    // 按相关性排序结果
    sortResultsByRelevance(results, originalQuery) {
        return results.sort((a, b) => {
            const scoreA = this.calculateFuzzyRelevance(a, originalQuery);
            const scoreB = this.calculateFuzzyRelevance(b, originalQuery);
            return scoreB - scoreA;
        });
    }

    // 计算模糊相关性分数
    calculateFuzzyRelevance(result, query) {
        const question = result.question || result;
        const language = this.i18n.getCurrentLanguage();
        
        let score = result.score || 0.5;
        
        const title = question.title?.[language]?.toLowerCase() || '';
        const content = question.content?.[language]?.toLowerCase() || '';
        const queryLower = query.toLowerCase();
        
        // 标题完全匹配
        if (title.includes(queryLower)) {
            score += 0.5;
        }
        
        // 内容部分匹配
        if (content.includes(queryLower)) {
            score += 0.2;
        }
        
        // 优先级加分
        if (question.priority === 'high') score += 0.15;
        else if (question.priority === 'medium') score += 0.1;
        
        // 分类相关性
        if (this.isCategoryRelevant(question.category, query)) {
            score += 0.1;
        }
        
        return Math.min(score, 1.0);
    }

    // 检查分类相关性
    isCategoryRelevant(categoryId, query) {
        const categories = this.dataManager.getCategories();
        const category = categories[categoryId];
        
        if (!category) return false;
        
        const language = this.i18n.getCurrentLanguage();
        const categoryName = category.name[language]?.toLowerCase() || '';
        
        return categoryName.includes(query.toLowerCase().substring(0, 2));
    }

    // 获取策略超时时间
    getStrategyTimeout(strategy) {
        const timeouts = {
            'streaming-ai': 8000,  // 8秒
            'standard-ai': 5000,   // 5秒  
            'fuzzy-match': 3000,   // 3秒
            'basic-search': 2000   // 2秒
        };
        
        return timeouts[strategy] || 5000;
    }

    // 验证结果有效性
    isValidResult(result) {
        return result && 
               result.results && 
               Array.isArray(result.results) && 
               result.results.length > 0 &&
               !result.error;
    }

    // 创建空结果
    createEmptyResult(query, language, error = null) {
        return {
            query: query,
            language: language,
            results: [],
            enhanced: false,
            strategy: 'none',
            fallbackLevel: -1,
            error: error?.message || '所有搜索策略都失败',
            totalAttempts: this.strategies.length
        };
    }

    // 记录成功
    recordSuccess(strategy) {
        this.successStats[strategy]++;
        console.debug(`📈 策略 ${strategy} 成功次数: ${this.successStats[strategy]}`);
    }

    // 记录错误
    recordError(strategy, error) {
        this.errorStats[strategy]++;
        console.debug(`📉 策略 ${strategy} 错误次数: ${this.errorStats[strategy]}`);
    }

    // 获取统计信息
    getStats() {
        const totalSuccess = Object.values(this.successStats).reduce((a, b) => a + b, 0);
        const totalErrors = Object.values(this.errorStats).reduce((a, b) => a + b, 0);
        const totalAttempts = totalSuccess + totalErrors;
        
        return {
            totalAttempts,
            totalSuccess,
            totalErrors,
            successRate: totalAttempts > 0 ? (totalSuccess / totalAttempts * 100).toFixed(2) + '%' : '0%',
            strategyStats: this.strategies.map(strategy => ({
                strategy,
                success: this.successStats[strategy],
                errors: this.errorStats[strategy],
                successRate: this.successStats[strategy] + this.errorStats[strategy] > 0 ? 
                    (this.successStats[strategy] / (this.successStats[strategy] + this.errorStats[strategy]) * 100).toFixed(1) + '%' : '0%'
            }))
        };
    }

    // 重置统计
    resetStats() {
        Object.keys(this.successStats).forEach(key => {
            this.successStats[key] = 0;
            this.errorStats[key] = 0;
        });
        console.log('📊 搜索策略统计已重置');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SearchFallbackManager;
} else {
    window.SearchFallbackManager = SearchFallbackManager;
}