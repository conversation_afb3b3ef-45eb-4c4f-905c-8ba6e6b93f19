// Gemini流式搜索引擎
// 实现四阶段搜索：基础搜索 → AI意图分析 → 搜索建议 → 优化结果

class StreamingSearchEngine {
    constructor(geminiAssistant, dataManager, i18n) {
        this.geminiAssistant = geminiAssistant;
        this.dataManager = dataManager;
        this.i18n = i18n;
        this.currentController = null;
        this.searchCache = new StreamingSearchCache();
        this.isStreaming = false;
        this.currentStage = null;
    }

    // 核心流式分析方法 - 四阶段处理
    async performStreamingAnalysis(query, language, onProgress, onComplete) {
        // 取消之前的流式处理
        this.cancelCurrentStream();
        
        this.isStreaming = true;
        this.currentController = new AbortController();
        
        const cacheKey = `${query.toLowerCase()}_${language}`;
        
        try {
            // 检查缓存
            const cachedResult = this.searchCache.get(cacheKey);
            if (cachedResult) {
                console.debug('使用缓存结果:', cacheKey);
                onComplete(cachedResult);
                return cachedResult;
            }

            const searchResults = {
                query,
                language,
                basicResults: [],
                enhancedResults: [],
                suggestions: [],
                intent: null,
                confidence: 0
            };

            // 阶段1: 立即显示基础搜索结果 
            this.updateStage('basic');
            const basicResults = await this.getBasicResults(query, language);
            searchResults.basicResults = basicResults;
            
            onProgress({
                stage: 'basic',
                results: basicResults,
                message: this.i18n.t('searchingBasic') || '搜索基础结果...'
            });

            if (!this.isStreaming) return null;

            // 阶段2: AI意图分析（如果可用）
            if (this.geminiAssistant && this.geminiAssistant.isAvailable()) {
                this.updateStage('ai-analysis');
                
                try {
                    const analysisResult = await this.performAIAnalysis(query, language, basicResults);
                    searchResults.intent = analysisResult.intent;
                    searchResults.confidence = analysisResult.confidence;
                    
                    onProgress({
                        stage: 'ai-analysis',
                        intent: analysisResult.intent,
                        confidence: analysisResult.confidence,
                        message: this.i18n.t('analyzingIntent') || 'AI意图分析中...'
                    });

                    if (!this.isStreaming) return null;

                    // 阶段3: 生成智能建议
                    this.updateStage('suggestions');
                    const suggestions = await this.generateSmartSuggestions(query, analysisResult, language);
                    searchResults.suggestions = suggestions;
                    
                    onProgress({
                        stage: 'suggestions',
                        suggestions: suggestions,
                        message: this.i18n.t('generatingSuggestions') || '生成智能建议...'
                    });

                    if (!this.isStreaming) return null;

                    // 阶段4: 优化搜索结果
                    this.updateStage('optimization');
                    const enhancedResults = await this.optimizeResults(
                        query, 
                        basicResults, 
                        analysisResult, 
                        suggestions, 
                        language
                    );
                    searchResults.enhancedResults = enhancedResults;
                    
                } catch (aiError) {
                    console.debug('AI增强搜索失败，使用基础结果:', aiError.message);
                    searchResults.enhancedResults = basicResults;
                }
            } else {
                // AI不可用，直接使用基础结果
                searchResults.enhancedResults = basicResults;
            }

            // 完成处理
            this.updateStage('complete');
            const finalResults = this.formatFinalResults(searchResults);
            
            // 缓存结果
            this.searchCache.set(cacheKey, finalResults);
            
            onComplete(finalResults);
            return finalResults;

        } catch (error) {
            if (error.name === 'AbortError') {
                console.log('流式分析被取消');
                return null;
            }
            
            console.error('流式分析失败:', error);
            
            // 降级到基础搜索
            try {
                const basicResults = await this.getBasicResults(query, language);
                const fallbackResult = {
                    query,
                    language,
                    results: basicResults,
                    enhanced: false,
                    error: true
                };
                onComplete(fallbackResult);
                return fallbackResult;
            } catch (fallbackError) {
                console.error('基础搜索也失败:', fallbackError);
                onComplete({ query, language, results: [], error: true });
                return null;
            }
        } finally {
            this.isStreaming = false;
            this.currentController = null;
            this.currentStage = null;
        }
    }

    // 阶段1: 获取基础搜索结果
    async getBasicResults(query, language) {
        console.debug(`🔍 执行基础搜索: "${query}"`);
        
        try {
            const results = await this.dataManager.searchQuestions(query, language);
            console.debug(`✅ 基础搜索完成，找到 ${results.length} 个结果`);
            return results.slice(0, 10); // 限制基础结果数量
        } catch (error) {
            console.error('基础搜索失败:', error);
            return [];
        }
    }

    // 阶段2: AI意图分析
    async performAIAnalysis(query, language, basicResults) {
        console.debug(`🧠 开始AI意图分析: "${query}"`);
        
        return new Promise((resolve, reject) => {
            if (!this.isStreaming) {
                reject(new Error('搜索已取消'));
                return;
            }

            let analysisResult = {
                intent: null,
                keywords: [],
                confidence: 0.5
            };

            this.geminiAssistant.streamingEnhanceSearchQuery(
                query,
                language,
                // 流式进度回调
                (progress) => {
                    if (!this.isStreaming) return;
                    
                    if (progress.stage === 'intent' && progress.intent) {
                        analysisResult.intent = progress.intent;
                    }
                    if (progress.keywords && progress.keywords.length > 0) {
                        analysisResult.keywords = [...new Set([...analysisResult.keywords, ...progress.keywords])];
                    }
                    if (progress.confidence) {
                        analysisResult.confidence = progress.confidence;
                    }
                },
                // 完成回调
                (finalResult) => {
                    if (!this.isStreaming) {
                        reject(new Error('搜索已取消'));
                        return;
                    }
                    
                    if (finalResult.enhanced) {
                        analysisResult = {
                            intent: finalResult.intent || analysisResult.intent,
                            keywords: finalResult.keywords || analysisResult.keywords,
                            confidence: finalResult.confidence || analysisResult.confidence
                        };
                    }
                    
                    console.debug('✅ AI意图分析完成:', analysisResult);
                    resolve(analysisResult);
                }
            );

            // 设置超时保护
            setTimeout(() => {
                if (this.isStreaming) {
                    console.debug('AI分析超时，使用部分结果');
                    resolve(analysisResult);
                }
            }, 5000); // 5秒超时
        });
    }

    // 阶段3: 生成智能建议
    async generateSmartSuggestions(query, analysisResult, language) {
        console.debug(`💡 生成智能搜索建议`);
        
        const suggestions = [];
        
        // 基于AI分析的关键词生成建议
        if (analysisResult.keywords && analysisResult.keywords.length > 0) {
            for (const keyword of analysisResult.keywords.slice(0, 3)) {
                if (keyword.toLowerCase() !== query.toLowerCase()) {
                    suggestions.push({
                        text: keyword,
                        type: 'keyword',
                        confidence: analysisResult.confidence || 0.7
                    });
                }
            }
        }

        // 基于意图类型生成相关建议
        if (analysisResult.intent) {
            const intentSuggestions = this.generateIntentBasedSuggestions(analysisResult.intent, language);
            suggestions.push(...intentSuggestions);
        }

        // 基于FAQ分类生成建议
        const categorySuggestions = this.generateCategorySuggestions(query, language);
        suggestions.push(...categorySuggestions.slice(0, 2));

        console.debug(`✅ 生成了 ${suggestions.length} 个智能建议`);
        return suggestions.slice(0, 5); // 限制建议数量
    }

    // 基于意图生成建议
    generateIntentBasedSuggestions(intent, language) {
        const intentMap = {
            'zh': {
                '技术': ['APP登录问题', '同步问题', '功能使用'],
                '财务': ['提现流程', '收入查询', '支付问题'],
                '服务': ['接单技巧', '客户沟通', '评价管理'],
                '注册': ['驾驶员注册', '认证流程', '资料上传'],
                '沟通': ['客户服务', '平台客服', '投诉处理'],
                '紧急': ['安全问题', '事故处理', '紧急联系']
            },
            'en': {
                'Technical': ['APP login', 'sync issues', 'features'],
                'Financial': ['withdrawal', 'income', 'payment'],
                'Service': ['order tips', 'customer chat', 'ratings'],
                'Registration': ['driver signup', 'verification', 'documents'],
                'Communication': ['customer service', 'support', 'complaints'],
                'Emergency': ['safety', 'accidents', 'emergency contact']
            },
            'ms': {
                'Teknikal': ['log masuk APP', 'isu sinkronisasi', 'ciri'],
                'Kewangan': ['pengeluaran', 'pendapatan', 'pembayaran'],
                'Perkhidmatan': ['tips pesanan', 'chat pelanggan', 'penilaian'],
                'Pendaftaran': ['daftar pemandu', 'pengesahan', 'dokumen'],
                'Komunikasi': ['khidmat pelanggan', 'sokongan', 'aduan'],
                'Kecemasan': ['keselamatan', 'kemalangan', 'hubungan kecemasan']
            }
        };

        const suggestions = [];
        const langIntentMap = intentMap[language] || intentMap['zh'];
        const intentSuggestions = langIntentMap[intent] || [];

        intentSuggestions.forEach(suggestion => {
            suggestions.push({
                text: suggestion,
                type: 'intent',
                confidence: 0.8
            });
        });

        return suggestions;
    }

    // 基于分类生成建议
    generateCategorySuggestions(query, language) {
        const suggestions = [];
        const categories = this.dataManager.getCategories();
        
        // 简单的关键词匹配逻辑
        const queryLower = query.toLowerCase();
        
        for (const [categoryId, category] of Object.entries(categories)) {
            const categoryName = category.name[language];
            if (categoryName && categoryName.toLowerCase().includes(queryLower.substring(0, 2))) {
                suggestions.push({
                    text: categoryName,
                    type: 'category',
                    categoryId: categoryId,
                    confidence: 0.6
                });
            }
        }

        return suggestions;
    }

    // 阶段4: 优化搜索结果
    async optimizeResults(query, basicResults, analysisResult, suggestions, language) {
        console.debug(`⚡ 优化搜索结果`);
        
        let optimizedResults = [...basicResults];
        
        // 基于AI分析的关键词进行二次搜索
        if (analysisResult.keywords && analysisResult.keywords.length > 0) {
            const enhancedQuery = analysisResult.keywords.join(' ');
            try {
                const enhancedResults = await this.dataManager.searchQuestions(enhancedQuery, language);
                
                // 合并结果并去重
                optimizedResults = this.mergeAndDeduplicateResults(basicResults, enhancedResults);
            } catch (error) {
                console.debug('增强搜索失败，使用基础结果:', error);
            }
        }

        // 根据置信度重新排序
        optimizedResults = this.reorderByRelevance(optimizedResults, query, analysisResult);
        
        console.debug(`✅ 结果优化完成，共 ${optimizedResults.length} 个结果`);
        return optimizedResults.slice(0, 15); // 限制最终结果数量
    }

    // 合并和去重结果
    mergeAndDeduplicateResults(basicResults, enhancedResults) {
        const seenIds = new Set();
        const merged = [];

        // 先添加基础结果
        for (const result of basicResults) {
            const id = result.id || result.question?.id;
            if (id && !seenIds.has(id)) {
                seenIds.add(id);
                merged.push(result);
            }
        }

        // 再添加增强结果
        for (const result of enhancedResults) {
            const id = result.id || result.question?.id;
            if (id && !seenIds.has(id)) {
                seenIds.add(id);
                merged.push(result);
            }
        }

        return merged;
    }

    // 根据相关性重新排序
    reorderByRelevance(results, query, analysisResult) {
        return results.sort((a, b) => {
            const scoreA = this.calculateRelevanceScore(a, query, analysisResult);
            const scoreB = this.calculateRelevanceScore(b, query, analysisResult);
            return scoreB - scoreA;
        });
    }

    // 计算相关性分数
    calculateRelevanceScore(result, query, analysisResult) {
        let score = result.score || 0.5; // 基础分数
        
        const question = result.question || result;
        const title = question.title?.[this.i18n.getCurrentLanguage()] || '';
        const content = question.content?.[this.i18n.getCurrentLanguage()] || '';

        // 标题匹配加分
        if (title.toLowerCase().includes(query.toLowerCase())) {
            score += 0.3;
        }

        // AI关键词匹配加分
        if (analysisResult.keywords) {
            for (const keyword of analysisResult.keywords) {
                if (title.toLowerCase().includes(keyword.toLowerCase()) ||
                    content.toLowerCase().includes(keyword.toLowerCase())) {
                    score += 0.2;
                }
            }
        }

        // 优先级加分
        if (question.priority === 'high') score += 0.1;
        else if (question.priority === 'medium') score += 0.05;

        return score;
    }

    // 格式化最终结果
    formatFinalResults(searchResults) {
        return {
            query: searchResults.query,
            language: searchResults.language,
            results: searchResults.enhancedResults.length > 0 ? 
                     searchResults.enhancedResults : searchResults.basicResults,
            suggestions: searchResults.suggestions,
            intent: searchResults.intent,
            confidence: searchResults.confidence,
            enhanced: searchResults.enhancedResults.length > 0,
            totalFound: searchResults.enhancedResults.length || searchResults.basicResults.length
        };
    }

    // 取消当前流式搜索
    cancelCurrentStream() {
        if (this.isStreaming && this.currentController) {
            this.currentController.abort();
            this.isStreaming = false;
            console.debug('流式搜索已取消');
        }
    }

    // 更新当前阶段
    updateStage(stage) {
        this.currentStage = stage;
        console.debug(`🔄 搜索阶段: ${stage}`);
    }

    // 获取当前状态
    getStatus() {
        return {
            isStreaming: this.isStreaming,
            currentStage: this.currentStage,
            cacheSize: this.searchCache.size()
        };
    }

    // 清理资源
    cleanup() {
        this.cancelCurrentStream();
        this.searchCache.clear();
    }
}

// 流式搜索专用缓存类
class StreamingSearchCache {
    constructor(maxSize = 50, ttl = 300000) { // 5分钟TTL
        this.cache = new Map();
        this.accessTimes = new Map();
        this.maxSize = maxSize;
        this.ttl = ttl;
    }

    set(key, value) {
        // 检查容量，LRU清理
        if (this.cache.size >= this.maxSize) {
            this.evictLRU();
        }
        
        this.cache.set(key, {
            data: value,
            timestamp: Date.now()
        });
        this.accessTimes.set(key, Date.now());
    }

    get(key) {
        const item = this.cache.get(key);
        if (!item) return null;
        
        // TTL检查
        if (Date.now() - item.timestamp > this.ttl) {
            this.cache.delete(key);
            this.accessTimes.delete(key);
            return null;
        }
        
        // 更新访问时间
        this.accessTimes.set(key, Date.now());
        return item.data;
    }

    evictLRU() {
        // 找到最久未访问的key
        let oldestKey = null;
        let oldestTime = Date.now();
        
        for (const [key, time] of this.accessTimes) {
            if (time < oldestTime) {
                oldestTime = time;
                oldestKey = key;
            }
        }
        
        if (oldestKey) {
            this.cache.delete(oldestKey);
            this.accessTimes.delete(oldestKey);
        }
    }

    clear() {
        this.cache.clear();
        this.accessTimes.clear();
    }

    size() {
        return this.cache.size;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { StreamingSearchEngine, StreamingSearchCache };
} else {
    window.StreamingSearchEngine = StreamingSearchEngine;
    window.StreamingSearchCache = StreamingSearchCache;
}