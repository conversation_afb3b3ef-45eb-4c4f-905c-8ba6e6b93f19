// 流式UI更新系统 - 处理渐进式搜索结果显示
(function() {
    'use strict';

    // 搜索UI渲染器
    class SearchUIRenderer {
        constructor(containerElement, i18nManager) {
            this.container = containerElement;
            this.i18n = i18nManager;
            this.currentResults = [];
            this.currentStage = 'idle';
            this.isAnimating = false;
            this.animationQueue = [];
            
            // 性能优化配置
            this.config = {
                maxVisibleResults: 50, // 虚拟滚动阈值
                animationDelay: 100,   // 结果动画间隔
                batchSize: 5,          // 批量更新大小
                debounceDelay: 50      // 更新防抖
            };

            // 防抖定时器
            this.updateTimer = null;
            
            console.log('SearchUIRenderer initialized');
        }

        // 更新搜索结果 - 主入口方法
        updateResults(data) {
            // 防抖更新
            if (this.updateTimer) {
                clearTimeout(this.updateTimer);
            }

            this.updateTimer = setTimeout(() => {
                this.performUpdate(data);
            }, this.config.debounceDelay);
        }

        // 执行UI更新
        performUpdate(data) {
            const { stage, results = [], enhancement, confidence = 0.8, timestamp } = data;
            
            console.debug(`Updating UI for stage: ${stage}, results: ${results.length}, confidence: ${confidence}`);

            // 更新当前状态
            this.currentStage = stage;
            this.currentResults = results;

            // 根据阶段执行不同的更新策略
            switch(stage) {
                case 'basic':
                    this.showBasicResults(results, { loading: true, confidence });
                    break;
                
                case 'fuzzy':
                    this.enhanceResults(results, { loading: true, confidence });
                    break;
                
                case 'ai-enhanced':
                case 'background-enhanced':
                    this.showEnhancedResults(results, enhancement, { confidence, stage });
                    break;
                
                case 'cached':
                    this.showCachedResults(results, { confidence });
                    break;
                
                case 'error':
                    this.showError(data.error);
                    break;
                
                default:
                    this.showFinalResults(results, { confidence });
            }

            // 更新进度指示器
            this.updateProgressIndicator(stage, confidence);
        }

        // 显示基础结果 (即时响应)
        showBasicResults(results, options = {}) {
            this.clearContainer();
            
            if (results.length === 0) {
                this.showNoResults('searching');
                return;
            }

            // 立即显示结果，添加加载指示器
            const fragment = this.createResultsFragment(results, {
                showLoadingIndicator: options.loading,
                highlightType: 'basic',
                confidence: options.confidence
            });

            this.container.appendChild(fragment);
            
            // 添加动画效果
            this.animateResults('fadeIn');
        }

        // 增强结果 (模糊搜索完成)
        enhanceResults(results, options = {}) {
            if (results.length === 0) return;

            // 智能差异更新
            const diff = this.calculateResultsDiff(this.currentResults, results);
            
            if (diff.hasChanges) {
                this.performIncrementalUpdate(results, {
                    diff,
                    highlightType: 'enhanced',
                    showLoadingIndicator: options.loading,
                    confidence: options.confidence
                });
            }
        }

        // 显示AI增强结果
        showEnhancedResults(results, enhancement, options = {}) {
            // AI增强完成，移除所有加载指示器
            this.removeLoadingIndicators();
            
            // 更新结果
            const fragment = this.createResultsFragment(results, {
                enhancement,
                highlightType: 'ai-enhanced',
                confidence: options.confidence,
                showAIBadge: true
            });

            // 平滑替换内容
            this.smoothReplaceContent(fragment);
            
            // 显示AI增强提示
            if (options.stage !== 'background-enhanced') {
                this.showAIEnhancementNotice(enhancement);
            }
        }

        // 显示缓存结果
        showCachedResults(results, options = {}) {
            this.clearContainer();
            
            const fragment = this.createResultsFragment(results, {
                highlightType: 'cached',
                confidence: options.confidence,
                showCachedBadge: true
            });

            this.container.appendChild(fragment);
            this.animateResults('fadeIn', { duration: 200 });
        }

        // 显示最终结果
        showFinalResults(results, options = {}) {
            this.removeLoadingIndicators();
            
            if (results.length === 0) {
                this.showNoResults('final');
                return;
            }

            const fragment = this.createResultsFragment(results, {
                highlightType: 'final',
                confidence: options.confidence
            });

            this.smoothReplaceContent(fragment);
        }

        // 创建结果片段
        createResultsFragment(results, options = {}) {
            const fragment = document.createDocumentFragment();
            
            // 处理虚拟滚动
            const visibleResults = this.getVisibleResults(results);
            
            visibleResults.forEach((result, index) => {
                const resultElement = this.createResultElement(result, {
                    ...options,
                    index,
                    total: results.length
                });
                
                fragment.appendChild(resultElement);
            });

            // 添加加载指示器
            if (options.showLoadingIndicator) {
                const loadingIndicator = this.createLoadingIndicator();
                fragment.appendChild(loadingIndicator);
            }

            return fragment;
        }

        // 创建单个结果元素
        createResultElement(result, options = {}) {
            const element = document.createElement('div');
            element.className = this.getResultElementClasses(result, options);
            element.dataset.resultId = result.id;
            element.dataset.score = result.score || 0;
            element.dataset.matchType = result.matchType || 'standard';

            // 构建HTML内容
            const currentLang = this.i18n.getCurrentLanguage();
            const title = result.title[currentLang] || result.title.zh || '';
            const content = this.extractContentPreview(result.content[currentLang] || result.content.zh || '');
            
            element.innerHTML = `
                <div class="search-result-header">
                    <h3 class="search-result-title">
                        ${this.highlightQuery(title, options.query)}
                        ${this.createResultBadges(result, options)}
                    </h3>
                    <div class="search-result-meta">
                        <span class="result-id">${result.id}</span>
                        <span class="result-score" title="相关性评分">${Math.round((result.score || 0) * 100)}%</span>
                        ${result.category ? `<span class="result-category">${this.getCategoryName(result.category)}</span>` : ''}
                    </div>
                </div>
                <div class="search-result-content">
                    <p>${this.highlightQuery(content, options.query)}</p>
                </div>
                <div class="search-result-actions">
                    <button class="btn-view-details" data-action="view" data-id="${result.id}">
                        ${this.i18n.t('viewDetails', '查看详情')}
                    </button>
                    ${result.relatedQuestions ? `
                        <span class="related-count">+${result.relatedQuestions.length} ${this.i18n.t('relatedQuestions', '相关问题')}</span>
                    ` : ''}
                </div>
            `;

            // 添加点击事件
            this.attachResultEvents(element, result);

            return element;
        }

        // 获取结果元素CSS类
        getResultElementClasses(result, options = {}) {
            const classes = ['search-result-item'];
            
            // 匹配类型样式
            if (result.matchType) {
                classes.push(`match-${result.matchType}`);
            }
            
            // 置信度样式
            const confidence = result.score || 0;
            if (confidence > 0.9) classes.push('high-confidence');
            else if (confidence > 0.7) classes.push('medium-confidence');
            else classes.push('low-confidence');
            
            // 优先级样式
            if (result.priority) {
                classes.push(`priority-${result.priority}`);
            }
            
            // 增强状态
            if (result.aiEnhanced) {
                classes.push('ai-enhanced');
            }
            
            // 动画状态
            if (options.animateIn) {
                classes.push('animate-in');
            }

            return classes.join(' ');
        }

        // 创建结果标识
        createResultBadges(result, options = {}) {
            const badges = [];

            if (options.showCachedBadge) {
                badges.push('<span class="result-badge badge-cached">⚡</span>');
            }

            if (options.showAIBadge && result.aiEnhanced) {
                badges.push('<span class="result-badge badge-ai">✨</span>');
            }

            if (result.priority === 'high') {
                badges.push('<span class="result-badge badge-priority">🔥</span>');
            }

            if (result.matchType === 'exact') {
                badges.push('<span class="result-badge badge-exact">🎯</span>');
            }

            return badges.length > 0 ? badges.join(' ') : '';
        }

        // 提取内容预览
        extractContentPreview(content, maxLength = 150) {
            const plainText = content.replace(/<[^>]+>/g, '');
            return plainText.length > maxLength ? 
                plainText.substring(0, maxLength) + '...' : 
                plainText;
        }

        // 高亮查询词
        highlightQuery(text, query) {
            if (!query) return text;
            
            const queryWords = query.split(' ').filter(w => w.length > 1);
            let highlightedText = text;
            
            queryWords.forEach(word => {
                const regex = new RegExp(`(${word})`, 'gi');
                highlightedText = highlightedText.replace(regex, '<mark class="query-highlight">$1</mark>');
            });
            
            return highlightedText;
        }

        // 计算结果差异
        calculateResultsDiff(oldResults, newResults) {
            const oldIds = new Set(oldResults.map(r => r.id));
            const newIds = new Set(newResults.map(r => r.id));
            
            const added = newResults.filter(r => !oldIds.has(r.id));
            const removed = oldResults.filter(r => !newIds.has(r.id));
            const updated = newResults.filter(r => {
                if (!oldIds.has(r.id)) return false;
                const oldResult = oldResults.find(or => or.id === r.id);
                return oldResult && oldResult.score !== r.score;
            });

            return {
                hasChanges: added.length > 0 || removed.length > 0 || updated.length > 0,
                added,
                removed,
                updated
            };
        }

        // 增量更新
        performIncrementalUpdate(results, options = {}) {
            const { diff } = options;
            
            // 移除已删除的结果
            diff.removed.forEach(result => {
                const element = this.container.querySelector(`[data-result-id="${result.id}"]`);
                if (element) {
                    this.animateOut(element);
                }
            });

            // 添加新结果
            diff.added.forEach((result, index) => {
                const element = this.createResultElement(result, {
                    ...options,
                    animateIn: true
                });
                
                // 找到合适的插入位置
                const insertPosition = this.findInsertPosition(result, results);
                this.insertElementAtPosition(element, insertPosition);
            });

            // 更新已存在的结果
            diff.updated.forEach(result => {
                const element = this.container.querySelector(`[data-result-id="${result.id}"]`);
                if (element) {
                    this.updateResultElement(element, result, options);
                }
            });
        }

        // 平滑替换内容
        smoothReplaceContent(newFragment) {
            if (this.isAnimating) {
                this.animationQueue.push(() => this.smoothReplaceContent(newFragment));
                return;
            }

            this.isAnimating = true;
            
            // 淡出现有内容
            this.container.style.opacity = '0.7';
            
            setTimeout(() => {
                this.container.innerHTML = '';
                this.container.appendChild(newFragment);
                this.container.style.opacity = '1';
                
                // 淡入新内容
                this.animateResults('slideIn', { 
                    duration: 300,
                    callback: () => {
                        this.isAnimating = false;
                        this.processAnimationQueue();
                    }
                });
            }, 150);
        }

        // 动画结果
        animateResults(animationType = 'fadeIn', options = {}) {
            const results = this.container.querySelectorAll('.search-result-item');
            const duration = options.duration || 300;
            
            results.forEach((element, index) => {
                element.style.animationDelay = `${index * this.config.animationDelay}ms`;
                element.classList.add(`animate-${animationType}`);
            });

            // 完成回调
            if (options.callback) {
                setTimeout(options.callback, duration + (results.length * this.config.animationDelay));
            }
        }

        // 更新进度指示器
        updateProgressIndicator(stage, confidence = 0.8) {
            let progressElement = this.container.parentElement?.querySelector('.search-progress');
            
            if (!progressElement) {
                progressElement = document.createElement('div');
                progressElement.className = 'search-progress';
                this.container.parentElement?.insertBefore(progressElement, this.container);
            }

            const stageMap = {
                'basic': { progress: 25, text: this.i18n.t('searchingBasic', '基础搜索中...') },
                'fuzzy': { progress: 50, text: this.i18n.t('searchingFuzzy', '模糊搜索中...') },
                'ai-enhanced': { progress: 100, text: this.i18n.t('searchComplete', '搜索完成') },
                'cached': { progress: 100, text: this.i18n.t('cachedResults', '缓存结果') },
                'error': { progress: 0, text: this.i18n.t('searchError', '搜索出错') }
            };

            const stageInfo = stageMap[stage] || { progress: 100, text: '' };
            
            progressElement.innerHTML = `
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${stageInfo.progress}%"></div>
                </div>
                <div class="progress-text">${stageInfo.text} (${Math.round(confidence * 100)}%)</div>
            `;

            // 完成后自动隐藏
            if (stageInfo.progress === 100) {
                setTimeout(() => {
                    if (progressElement) {
                        progressElement.style.opacity = '0';
                        setTimeout(() => progressElement.remove(), 300);
                    }
                }, 1500);
            }
        }

        // 显示无结果
        showNoResults(type = 'final') {
            this.clearContainer();
            
            const messages = {
                'searching': this.i18n.t('searching', '搜索中...'),
                'final': this.i18n.t('noResults', '未找到相关结果'),
                'error': this.i18n.t('searchError', '搜索出错，请重试')
            };

            this.container.innerHTML = `
                <div class="no-results">
                    <div class="no-results-icon">${type === 'searching' ? '🔍' : '😅'}</div>
                    <div class="no-results-message">${messages[type]}</div>
                </div>
            `;
        }

        // 显示错误
        showError(error) {
            this.clearContainer();
            
            this.container.innerHTML = `
                <div class="search-error">
                    <div class="error-icon">⚠️</div>
                    <div class="error-message">${error || this.i18n.t('unknownError', '未知错误')}</div>
                    <button class="btn-retry" onclick="window.app?.retrySearch()">
                        ${this.i18n.t('retry', '重试')}
                    </button>
                </div>
            `;
        }

        // 工具方法
        clearContainer() {
            this.container.innerHTML = '';
        }

        removeLoadingIndicators() {
            const indicators = this.container.querySelectorAll('.loading-indicator');
            indicators.forEach(indicator => indicator.remove());
        }

        createLoadingIndicator() {
            const indicator = document.createElement('div');
            indicator.className = 'loading-indicator';
            indicator.innerHTML = `
                <div class="loading-spinner"></div>
                <div class="loading-text">${this.i18n.t('enhancing', 'AI增强中...')}</div>
            `;
            return indicator;
        }

        showAIEnhancementNotice(enhancement) {
            if (!enhancement || !enhancement.enhanced) return;

            const notice = document.createElement('div');
            notice.className = 'ai-enhancement-notice';
            notice.innerHTML = `
                <span class="ai-icon">✨</span>
                ${this.i18n.t('aiEnhanced', 'AI已优化搜索结果')}
                ${enhancement.keywords ? `<small>(${enhancement.keywords.slice(0, 3).join(', ')})</small>` : ''}
            `;

            this.container.insertBefore(notice, this.container.firstChild);

            // 3秒后自动隐藏
            setTimeout(() => {
                notice.style.opacity = '0';
                setTimeout(() => notice.remove(), 300);
            }, 3000);
        }

        // 事件处理
        attachResultEvents(element, result) {
            const viewButton = element.querySelector('.btn-view-details');
            if (viewButton) {
                viewButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.handleResultClick(result);
                });
            }

            // 整个结果项点击
            element.addEventListener('click', () => {
                this.handleResultClick(result);
            });
        }

        handleResultClick(result) {
            // 触发自定义事件
            const event = new CustomEvent('resultClick', {
                detail: { result },
                bubbles: true
            });
            this.container.dispatchEvent(event);
        }

        // 获取分类名称
        getCategoryName(categoryId) {
            const currentLang = this.i18n.getCurrentLanguage();
            // 这里应该通过分类管理器获取分类名称
            return categoryId; // 简化实现
        }

        // 虚拟滚动相关
        getVisibleResults(results) {
            // 如果结果少于阈值，直接返回所有结果
            if (results.length <= this.config.maxVisibleResults) {
                return results;
            }
            
            // 这里可以实现更复杂的虚拟滚动逻辑
            return results.slice(0, this.config.maxVisibleResults);
        }

        findInsertPosition(result, allResults) {
            const targetScore = result.score || 0;
            const existingElements = Array.from(this.container.querySelectorAll('.search-result-item'));
            
            for (let i = 0; i < existingElements.length; i++) {
                const elementScore = parseFloat(existingElements[i].dataset.score) || 0;
                if (targetScore > elementScore) {
                    return i;
                }
            }
            
            return existingElements.length;
        }

        insertElementAtPosition(element, position) {
            const existingElements = this.container.querySelectorAll('.search-result-item');
            
            if (position >= existingElements.length) {
                this.container.appendChild(element);
            } else {
                this.container.insertBefore(element, existingElements[position]);
            }
        }

        updateResultElement(element, result, options) {
            // 更新评分
            element.dataset.score = result.score || 0;
            
            // 更新评分显示
            const scoreElement = element.querySelector('.result-score');
            if (scoreElement) {
                scoreElement.textContent = `${Math.round((result.score || 0) * 100)}%`;
            }
            
            // 添加更新动画
            element.classList.add('updated');
            setTimeout(() => element.classList.remove('updated'), 500);
        }

        animateOut(element) {
            element.style.transition = 'all 0.3s ease';
            element.style.opacity = '0';
            element.style.transform = 'translateX(-20px)';
            
            setTimeout(() => element.remove(), 300);
        }

        processAnimationQueue() {
            if (this.animationQueue.length > 0) {
                const nextAnimation = this.animationQueue.shift();
                nextAnimation();
            }
        }

        // 公共方法
        reset() {
            this.clearContainer();
            this.currentResults = [];
            this.currentStage = 'idle';
            this.isAnimating = false;
            this.animationQueue = [];
        }

        getCurrentResults() {
            return this.currentResults;
        }

        getCurrentStage() {
            return this.currentStage;
        }
    }

    // 导出到全局
    if (typeof window !== 'undefined') {
        window.SearchUIRenderer = SearchUIRenderer;
    }
    
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = SearchUIRenderer;
    }
})();