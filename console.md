data.js:25729 DataManager initialized with unified category system
app.js:11 📋 DataManager初始化完成
app.js:13 📊 可用分类数量: 6
app.js:14 📊 分类列表: (6) ['technical', 'financial', 'service', 'registration', 'communication', 'emergency']
app.js:22 ✅ 分类系统验证通过
app.js:353 ✅ 全局错误处理器设置完成
unified-search-engine.js:345 UnifiedSearchEngine initialized
unified-search-engine.js:359 初始化高性能RAG向量搜索引擎...
rag-vector-engine.js:426 高性能RAG向量搜索引擎初始化
rag-vector-engine.js:427 配置: {maxResults: 15, similarityThreshold: 0.05, enableCache: true, cacheSize: 100, enableParallelProcessing: true, …}
rag-vector-engine.js:432 从FAQ数据初始化向量引擎...
rag-vector-engine.js:182 构建高性能词汇表...
rag-vector-engine.js:224 高性能词汇表构建完成: 980 个词汇, 50ms
rag-vector-engine.js:466 并行向量化文档...
rag-vector-engine.js:740 并行向量化处理 94 个文档...
app.js:73 ✅ 统一搜索引擎初始化成功
search-ui-renderer.js:26 SearchUIRenderer initialized
app.js:83 ✅ 搜索UI渲染器初始化成功
app.js:99 ✅ 流式搜索引擎初始化成功 (兼容模式)
app.js:113 ✅ 搜索降级策略管理器初始化成功
performance-optimizer.js:31 ✅ 性能优化器初始化完成
app.js:136 ✅ 性能优化器初始化成功
mobile-interaction.js:43 📱 初始化移动端交互管理器...
mobile-interaction.js:63 ✅ 移动端交互管理器初始化完成
app.js:153 ✅ 移动端交互管理器初始化成功
smart-suggestions.js:38 ✅ 智能搜索建议管理器初始化完成
app.js:175 ✅ 智能搜索建议管理器初始化成功
mobile-optimization.js:43 📱 移动端优化管理器初始化完成 {isIOS: false, isAndroid: false, isMobile: false, isTablet: false, isSafari: false, …}
app.js:193 ✅ 移动端优化管理器初始化成功
system-validator.js:35 🧪 系统验证器初始化完成
app.js:208 ✅ 系统验证器初始化成功
app.js:223 ⚠️ PerformanceBenchmarkManager未加载
app.js:531 🚀 开始初始化FAQ应用...
mobile-interaction.js:109 📱 长按监听器已添加
mobile-interaction.js:123 📱 双击监听器已添加
mobile-interaction.js:137 📱 滑动监听器已添加
mobile-interaction.js:137 📱 滑动监听器已添加
mobile-interaction.js:137 📱 滑动监听器已添加
app.js:1961 ✅ 移动端交互功能设置完成
mobile-optimization.js:58 ✅ 移动端优化设置完成
app.js:2355 ✅ 移动端优化功能设置完成
app.js:534 ✅ 事件监听器设置完成
app.js:1496 ✅ 渲染了 6 个分类卡片到欢迎页面
app.js:541 ✅ 快速开始按钮渲染完成
app.js:544 ✅ 欢迎页面显示完成
app.js:547 ✅ 返回顶部按钮设置完成
app.js:550 ✅ 国际化文本更新完成
app.js:554 ✅ URL参数处理完成
app.js:556 🎉 FAQ应用初始化成功！
app.js:3937 FAQ App initialized successfully
app.js:3908 [Violation] 'DOMContentLoaded' handler took 388ms
rag-vector-engine.js:756 向量化进度: 50/94
rag-vector-engine.js:760 并行向量化完成
rag-vector-engine.js:845 执行向量内存压缩...
rag-vector-engine.js:857 向量压缩完成
rag-vector-engine.js:487 RAG向量引擎初始化完成: 94 个文档, 318ms
unified-search-engine.js:380 ✅ RAG向量搜索引擎初始化成功: {success: true, documentsCount: 94, vocabularySize: 980, initTime: 318}
app.js:2360 ⌨️ 移动端键盘打开: 0px
mobile-interaction.js:749 📱 触觉反馈被跳过：等待用户首次交互
mobile-optimization.js:170 ⌨️ 键盘状态变化: 打开 高度: 0px
app.js:258 🧠 RAG向量搜索引擎初始化完成
app.js:263 📊 RAG统计: 94 个文档, 980 个词汇
app.js:293 🔄 预加载常用向量查询...
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:793 📊 RAG搜索调试: 阈值0.2过滤后0个结果
rag-vector-engine.js:796 📊 RAG搜索调试: 尝试放宽阈值到0.06
rag-vector-engine.js:604 RAG高性能语义搜索: "登录问题" -> 3 个结果, 9ms
data.js:26382 🚀 GoMyHire FAQ 统一分类系统已加载
data.js:26383 📋 可用组件:
data.js:26384   - DataManager: 主数据管理器
data.js:26385   - EnhancedDataManager: 增强数据管理器
data.js:26386   - EnhancedSearchManager: 增强搜索管理器
data.js:26387   - CategoryAdapter: 分类适配器
data.js:26388   - unifiedCategorySystem: 统一分类系统
data.js:26389   - unifiedSearchTags: 统一搜索标签
data.js:26359 🔍 快速分类映射检查...
data.js:26367 📊 FAQ问题中使用的分类ID:
data.js:26373   service -> service ✅
data.js:26373   communication -> communication ✅
data.js:26373   technical -> technical ✅
data.js:26373   registration -> registration ✅
data.js:26373   emergency -> emergency ✅
data.js:26373   financial -> financial ✅
data.js:26251 🔍 开始验证统一分类系统...
data.js:25729 DataManager initialized with unified category system
data.js:26258 
📋 检查FAQ问题分类映射:
data.js:26264 📊 FAQ问题中使用的分类ID:
data.js:26268   service -> service ✅
data.js:26268   communication -> communication ✅
data.js:26268   technical -> technical ✅
data.js:26268   registration -> registration ✅
data.js:26268   emergency -> emergency ✅
data.js:26268   financial -> financial ✅
data.js:26274 
📊 验证结果:
data.js:26275 ✅ 总分类数: 6
data.js:26276 ✅ 总问题数: 94
data.js:26277 ✅ 已映射问题: 94
data.js:26278 ❌ 未映射问题: 0
data.js:26281 🎉 统一分类系统验证通过！
data.js:26299 
📋 可用分类:
data.js:26304   📱 技术问题 (technical): 23 个问题
data.js:26304   💰 财务问题 (financial): 6 个问题
data.js:26304   🛎️ 服务流程 (service): 33 个问题
data.js:26304   🚗 注册入门 (registration): 14 个问题
data.js:26304   💬 沟通管理 (communication): 7 个问题
data.js:26304   🚨 紧急处理 (emergency): 11 个问题
data.js:26308 
📈 分类统计:
data.js:26313   📱 技术问题: 23 个问题
data.js:26313   💰 财务问题: 6 个问题
data.js:26313   🛎️ 服务流程: 33 个问题
data.js:26313   🚗 注册入门: 14 个问题
data.js:26313   💬 沟通管理: 7 个问题
data.js:26313   🚨 紧急处理: 11 个问题
data.js:26321 
🔍 测试搜索功能...
data.js:25729 DataManager initialized with unified category system
data.js:26380 [Violation] 'setTimeout' handler took 256ms
data.js:26337   "登录": 找到 19 个结果
data.js:26337   "Ctrip": 找到 8 个结果
data.js:26337   "提现": 找到 5 个结果
data.js:26337   "举牌": 找到 9 个结果
data.js:26337   "评分": 找到 14 个结果
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:793 📊 RAG搜索调试: 阈值0.2过滤后0个结果
rag-vector-engine.js:796 📊 RAG搜索调试: 尝试放宽阈值到0.06
rag-vector-engine.js:806 📊 RAG搜索调试: 返回Top3最相似结果作为fallback
rag-vector-engine.js:604 RAG高性能语义搜索: "支付问题" -> 3 个结果, 7ms
rag-vector-engine.js:793 📊 RAG搜索调试: 阈值0.2过滤后0个结果
rag-vector-engine.js:796 📊 RAG搜索调试: 尝试放宽阈值到0.06
rag-vector-engine.js:806 📊 RAG搜索调试: 返回Top3最相似结果作为fallback
rag-vector-engine.js:604 RAG高性能语义搜索: "接单流程" -> 3 个结果, 3ms
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:793 📊 RAG搜索调试: 阈值0.2过滤后0个结果
rag-vector-engine.js:796 📊 RAG搜索调试: 尝试放宽阈值到0.06
rag-vector-engine.js:806 📊 RAG搜索调试: 返回Top3最相似结果作为fallback
rag-vector-engine.js:604 RAG高性能语义搜索: "提现失败" -> 3 个结果, 6ms
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:604 RAG高性能语义搜索: "login issue" -> 1 个结果, 2ms
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:793 📊 RAG搜索调试: 阈值0.2过滤后0个结果
rag-vector-engine.js:796 📊 RAG搜索调试: 尝试放宽阈值到0.06
rag-vector-engine.js:604 RAG高性能语义搜索: "payment problem" -> 3 个结果, 4ms
gemini-assistant.js:781 API连接测试成功: 连接成功
app.js:38 ✅ Gemini API 连接成功
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:604 RAG高性能语义搜索: "order flow" -> 3 个结果, 2ms
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:604 RAG高性能语义搜索: "withdrawal failed" -> 2 个结果, 5ms
app.js:1273 开始预加载常用搜索...
unified-search-engine.js:940 开始预加载 15 个常用查询...
data.js:25848 Quick search completed in 1ms, found 0 results
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："如何接单"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）
2....
gemini-assistant.js:807 使用自适应超时: 第1次尝试, 超时3000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 5ms, found 0 results
rag-vector-engine.js:793 📊 RAG搜索调试: 阈值0.05过滤后0个结果
rag-vector-engine.js:796 📊 RAG搜索调试: 尝试放宽阈值到0.015
rag-vector-engine.js:806 📊 RAG搜索调试: 返回Top3最相似结果作为fallback
rag-vector-engine.js:604 RAG高性能语义搜索: "如何接单" -> 3 个结果, 9ms
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:604 RAG高性能语义搜索: "masalah log masuk" -> 1 个结果, 1ms
data.js:25848 Quick search completed in 1ms, found 3 results
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："提现"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）
2. 用...
gemini-assistant.js:807 使用自适应超时: 第1次尝试, 超时3000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 2ms, found 3 results
rag-vector-engine.js:604 RAG高性能语义搜索: "提现" -> 2 个结果, 4ms
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:793 📊 RAG搜索调试: 阈值0.2过滤后0个结果
rag-vector-engine.js:796 📊 RAG搜索调试: 尝试放宽阈值到0.06
rag-vector-engine.js:604 RAG高性能语义搜索: "masalah pembayaran" -> 3 个结果, 2ms
data.js:25848 Quick search completed in 3ms, found 1 results
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："账号"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）
2. 用...
gemini-assistant.js:807 使用自适应超时: 第1次尝试, 超时3000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 2ms, found 1 results
rag-vector-engine.js:604 RAG高性能语义搜索: "账号" -> 4 个结果, 5ms
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："评分"，语言是中文。

请注意：所有回复请使用 中文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）
2. 用户的...
gemini-assistant.js:807 使用自适应超时: 第1次尝试, 超时3000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
data.js:25848 Quick search completed in 1ms, found 6 results
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："车辆"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）
2. 用...
gemini-assistant.js:807 使用自适应超时: 第1次尝试, 超时3000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 4ms, found 6 results
rag-vector-engine.js:793 📊 RAG搜索调试: 阈值0.05过滤后0个结果
rag-vector-engine.js:796 📊 RAG搜索调试: 尝试放宽阈值到0.015
rag-vector-engine.js:806 📊 RAG搜索调试: 返回Top3最相似结果作为fallback
rag-vector-engine.js:604 RAG高性能语义搜索: "车辆" -> 3 个结果, 7ms
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:793 📊 RAG搜索调试: 阈值0.2过滤后0个结果
rag-vector-engine.js:796 📊 RAG搜索调试: 尝试放宽阈值到0.06
rag-vector-engine.js:604 RAG高性能语义搜索: "aliran pesanan" -> 3 个结果, 1ms
app.js:311 ✅ 向量查询预加载完成
data.js:25848 Quick search completed in 1ms, found 3 results
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："客服"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）
2. 用...
gemini-assistant.js:807 使用自适应超时: 第1次尝试, 超时3000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 4ms, found 4 results
rag-vector-engine.js:604 RAG高性能语义搜索: "客服" -> 5 个结果, 6ms
data.js:25848 Quick search completed in 0ms, found 0 results
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："how to receive orders"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关...
gemini-assistant.js:807 使用自适应超时: 第1次尝试, 超时3000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 40ms, found 15 results
rag-vector-engine.js:604 RAG高性能语义搜索: "how to receive orders" -> 7 个结果, 42ms
data.js:25848 Quick search completed in 1ms, found 3 results
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："withdrawal"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关...
gemini-assistant.js:807 使用自适应超时: 第1次尝试, 超时3000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 22ms, found 3 results
rag-vector-engine.js:604 RAG高性能语义搜索: "withdrawal" -> 3 个结果, 24ms
data.js:25848 Quick search completed in 0ms, found 3 results
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："account"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）...
gemini-assistant.js:807 使用自适应超时: 第1次尝试, 超时3000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 12ms, found 3 results
rag-vector-engine.js:604 RAG高性能语义搜索: "account" -> 7 个结果, 15ms
data.js:25848 Quick search completed in 0ms, found 5 results
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："vehicle"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）...
gemini-assistant.js:807 使用自适应超时: 第1次尝试, 超时3000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 8ms, found 6 results
rag-vector-engine.js:604 RAG高性能语义搜索: "vehicle" -> 8 个结果, 11ms
data.js:25848 Quick search completed in 0ms, found 0 results
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："customer service"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括...
gemini-assistant.js:807 使用自适应超时: 第1次尝试, 超时3000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 56ms, found 10 results
rag-vector-engine.js:604 RAG高性能语义搜索: "customer service" -> 8 个结果, 58ms
data.js:25848 Quick search completed in 1ms, found 0 results
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："cara menerima pesanan"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关...
gemini-assistant.js:807 使用自适应超时: 第1次尝试, 超时3000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 26ms, found 15 results
rag-vector-engine.js:604 RAG高性能语义搜索: "cara menerima pesanan" -> 4 个结果, 28ms
data.js:25848 Quick search completed in 0ms, found 3 results
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："pengeluaran"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相...
gemini-assistant.js:807 使用自适应超时: 第1次尝试, 超时3000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 32ms, found 5 results
rag-vector-engine.js:604 RAG高性能语义搜索: "pengeluaran" -> 2 个结果, 34ms
data.js:25848 Quick search completed in 1ms, found 3 results
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："akaun"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）
2...
gemini-assistant.js:807 使用自适应超时: 第1次尝试, 超时3000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 10ms, found 15 results
rag-vector-engine.js:604 RAG高性能语义搜索: "akaun" -> 2 个结果, 12ms
data.js:25848 Quick search completed in 1ms, found 6 results
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："kenderaan"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术...
gemini-assistant.js:807 使用自适应超时: 第1次尝试, 超时3000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 10ms, found 15 results
rag-vector-engine.js:604 RAG高性能语义搜索: "kenderaan" -> 3 个结果, 12ms
unified-search-engine.js:593 AI enhancement failed (non-critical): Timeout after 2000ms
data.js:25848 Quick search completed in 0ms, found 2 results
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："khidmat pelanggan"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包...
gemini-assistant.js:807 使用自适应超时: 第1次尝试, 超时3000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 13ms, found 15 results
rag-vector-engine.js:604 RAG高性能语义搜索: "khidmat pelanggan" -> 6 个结果, 15ms
unified-search-engine.js:593 AI enhancement failed (non-critical): Timeout after 2000ms
unified-search-engine.js:593 AI enhancement failed (non-critical): Timeout after 2000ms
unified-search-engine.js:593 AI enhancement failed (non-critical): Timeout after 2000ms
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'uY-uaJP3C7qP6dkP9Juz4A8'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": [
        "cara terima tempahan",
        "cara dapatkan pesanan",
        "cara ambil order",
        "panduan pemandu terima tempahan",
        "aplikasi pemandu terima tempahan",
        "tips terima tempahan",
        "cara mula terima tempahan",
        "proses terima tempahan",
        "syarat terima tempahan",
        "cara aktifkan terima tempahan"
    ],
    "intent": "Pengguna ingin mengetahui langkah-langkah dan cara yang betul untuk menerima pesanan atau tempahan melalui aplikasi pemandu, termasuk cara mengaktifkan ciri penerimaan tempahan, memahami notifikasi tempahan, dan proses penerimaan tempahan.",
    "suggestions": [
        "Cara mendaftar sebagai pemandu",
        "Cara menggunakan aplikasi pemandu",
        "Cara menguruskan pembayaran tempahan",
        "Cara mendapatkan rating yang baik",
        "Peraturan keselamatan pemandu",
        "Jenis-jenis kenderaan yang diterima"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(10), intent: 'Pengguna ingin mengetahui langkah-langkah dan cara…ifikasi tempahan, dan proses penerimaan tempahan.', suggestions: Array(6)}
gemini-assistant.js:60 Gemini enhancement successful in 2473ms: {keywords: Array(10), intent: 'Pengguna ingin mengetahui langkah-langkah dan cara…ifikasi tempahan, dan proses penerimaan tempahan.', suggestions: Array(6)}
unified-search-engine.js:593 AI enhancement failed (non-critical): Timeout after 2000ms
unified-search-engine.js:593 AI enhancement failed (non-critical): Timeout after 2000ms
unified-search-engine.js:593 AI enhancement failed (non-critical): Timeout after 2000ms
unified-search-engine.js:593 AI enhancement failed (non-critical): Timeout after 2000ms
gemini-assistant.js:82 API超时，尝试第2次重试...
unified-search-engine.js:593 AI enhancement failed (non-critical): Timeout after 2000ms
gemini-assistant.js:82 API超时，尝试第2次重试...
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'uo-uaNCPBJziqtsP5r-mgQc'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": ["keluar wang", "pengeluaran tunai", "tarik balik dana", "duit masuk akaun", "cara keluarkan pendapatan", "pembayaran pemandu"],
    "intent": "Pemandu mencari maklumat tentang cara mengeluarkan wang atau pendapatan yang diperolehi melalui aplikasi pemanduan.",
    "suggestions": [
        "Cara membuat pengeluaran pendapatan pemandu",
        "Tempoh masa pengeluaran tunai",
        "Kaedah pembayaran yang disokong untuk pengeluaran",
        "Yuran pengeluaran wang",
        "Menyemak status pengeluaran"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(6), intent: 'Pemandu mencari maklumat tentang cara mengeluarkan…patan yang diperolehi melalui aplikasi pemanduan.', suggestions: Array(5)}
gemini-assistant.js:82 API超时，尝试第2次重试...
unified-search-engine.js:593 AI enhancement failed (non-critical): Timeout after 2000ms
gemini-assistant.js:82 API超时，尝试第2次重试...
unified-search-engine.js:593 AI enhancement failed (non-critical): Timeout after 2000ms
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'uo-uaKyVEeXzqtsPzYSo2Ao'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": ["akaun", "pengguna", "profil", "daftar", "masuk", "log masuk", "pengurusan akaun", "butiran akaun", "kemaskini akaun"],
    "intent": "Pengguna mencari maklumat berkaitan dengan akaun mereka sebagai pemandu, kemungkinan besar untuk mendaftar, log masuk, menguruskan profil, atau mengemaskini maklumat peribadi mereka dalam aplikasi pemandu.",
    "suggestions": [
        "Cara mendaftar akaun pemandu",
        "Bagaimana untuk log masuk ke akaun pemandu",
        "Cara mengemaskini maklumat profil pemandu",
        "Apa yang perlu dilakukan jika terlupa kata laluan akaun",
        "Pengurusan akaun pemandu dalam aplikasi"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(9), intent: 'Pengguna mencari maklumat berkaitan dengan akaun m… maklumat peribadi mereka dalam aplikasi pemandu.', suggestions: Array(5)}
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'uo-uaOLKEs2hqtsPx7O6-Ao'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": ["评分", "评价", "分数", "星级", "好评", "差评", "客户评价", "司机评分", "服务评分", "订单评分"],
    "intent": "用户可能想了解如何查看自己的司机评分、如何提高评分、评分对司机有什么影响，或者想了解平台关于司机评分的规则和标准。",
    "suggestions": [
        "如何查看我的司机评分？",
        "如何提高我的司机评分？",
        "司机评分标准是什么？",
        "评分对我的收入有影响吗？",
        "如何处理差评？"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(10), intent: '用户可能想了解如何查看自己的司机评分、如何提高评分、评分对司机有什么影响，或者想了解平台关于司机评分的规则和标准。', suggestions: Array(5)}
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："提现"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）
2. 用...
gemini-assistant.js:807 使用自适应超时: 第2次尝试, 超时6000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
gemini-assistant.js:82 API超时，尝试第2次重试...
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'uo-uaIPuF8GuqtsP9_DauAU'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": ["khidmat pelanggan", "sokongan pelanggan", "bantuan pemandu", "masalah aplikasi", "pertanyaan akaun", "isu pembayaran", "laporan pemandu", "panduan pemandu", "soalan lazim pemandu"],
    "intent": "Pengguna mencari bantuan atau maklumat mengenai perkhidmatan pelanggan yang berkaitan dengan aplikasi pemandu, seperti penyelesaian masalah, pertanyaan akaun, isu pembayaran, atau panduan penggunaan aplikasi.",
    "suggestions": [
        "Cara menghubungi khidmat pelanggan",
        "Soalan lazim mengenai aplikasi pemandu",
        "Cara melaporkan masalah akaun",
        "Bantuan mengenai proses pembayaran",
        "Panduan penggunaan ciri-ciri aplikasi"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(9), intent: 'Pengguna mencari bantuan atau maklumat mengenai pe…isu pembayaran, atau panduan penggunaan aplikasi.', suggestions: Array(5)}
unified-search-engine.js:593 AI enhancement failed (non-critical): Timeout after 2000ms
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："账号"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）
2. 用...
gemini-assistant.js:807 使用自适应超时: 第2次尝试, 超时6000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
gemini-assistant.js:82 API超时，尝试第2次重试...
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："评分"，语言是中文。

请注意：所有回复请使用 中文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）
2. 用户的...
gemini-assistant.js:807 使用自适应超时: 第2次尝试, 超时6000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
unified-search-engine.js:593 AI enhancement failed (non-critical): Timeout after 2000ms
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'uo-uaKq9IsyQmtkPxtWYyQo'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": ["kenderaan", "kereta", "motor", "lesen memandu", "pendaftaran kenderaan", "insurans kenderaan", "servis kenderaan", "penyelenggaraan kenderaan", "jenis kenderaan", "syarat kenderaan"],
    "intent": "Pengguna mencari maklumat umum mengenai kenderaan, kemungkinan besar berkaitan dengan penggunaan kenderaan untuk tujuan pemanduan profesional (seperti pemandu e-hailing atau penghantaran), atau mungkin berkaitan dengan pendaftaran, lesen, insurans, atau penyelenggaraan kenderaan mereka.",
    "suggestions": [
        "Bagaimana mendaftar kenderaan saya untuk aplikasi pemanduan?",
        "Jenis kenderaan apa yang diterima untuk perkhidmatan e-hailing?",
        "Apa yang perlu saya ketahui tentang insurans kenderaan untuk pemandu?",
        "Bagaimana cara memeriksa status pendaftaran kenderaan saya?",
        "Panduan penyelenggaraan kenderaan untuk pemandu profesional."
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(10), intent: 'Pengguna mencari maklumat umum mengenai kenderaan,… insurans, atau penyelenggaraan kenderaan mereka.', suggestions: Array(5)}
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："车辆"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）
2. 用...
gemini-assistant.js:807 使用自适应超时: 第2次尝试, 超时6000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
gemini-assistant.js:82 API超时，尝试第2次重试...
unified-search-engine.js:593 AI enhancement failed (non-critical): Timeout after 2000ms
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："客服"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）
2. 用...
gemini-assistant.js:807 使用自适应超时: 第2次尝试, 超时6000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
gemini-assistant.js:82 API超时，尝试第2次重试...
unified-search-engine.js:593 AI enhancement failed (non-critical): Timeout after 2000ms
unified-search-engine.js:815 搜索性能超过预算: 2014ms > 2000ms
recordThreeTrackMetrics @ unified-search-engine.js:815
search @ unified-search-engine.js:496
await in search
(anonymous) @ unified-search-engine.js:946
setTimeout
(anonymous) @ unified-search-engine.js:945
preloadCommonQueries @ unified-search-engine.js:944
preloadCommonSearches @ app.js:1274
(anonymous) @ app.js:566
setTimeout
init @ app.js:565
FAQApp @ app.js:226
initializeApp @ app.js:3935
unified-search-engine.js:949 预加载完成 (1/15): "khidmat pelanggan" -> 15 个结果
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'uo-uaM-vNejlqtsP9OCr4Ao'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": [
        "cara terima tempahan",
        "dapat pesanan",
        "urus tempahan",
        "terima kerja",
        "aplikasi pemandu",
        "panduan pemandu",
        "pesanan baru",
        "cara mula memandu",
        "syarat terima tempahan",
        "tips terima tempahan"
    ],
    "intent": "Pengguna ingin mengetahui langkah-langkah dan cara yang betul untuk menerima pesanan atau tempahan melalui aplikasi pemandu, serta tips untuk menguruskan tempahan tersebut secara efisien.",
    "suggestions": [
        "Cara menerima tempahan pertama anda",
        "Panduan penggunaan aplikasi pemandu untuk menerima pesanan",
        "Tips menguruskan banyak tempahan sekaligus",
        "Apa yang perlu dilakukan apabila menerima tempahan yang dibatalkan",
        "Cara menolak tempahan dengan betul"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(10), intent: 'Pengguna ingin mengetahui langkah-langkah dan cara…tuk menguruskan tempahan tersebut secara efisien.', suggestions: Array(5)}
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："how to receive orders"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关...
gemini-assistant.js:807 使用自适应超时: 第2次尝试, 超时6000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
gemini-assistant.js:82 API超时，尝试第2次重试...
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'uo-uaL3WO5jxqtsPqMzb6Ao'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": ["pengeluaran", "tarik balik", "tunai", "wang", "gaji", "pendapatan", "pembayaran", "akaun", "dompet digital", "e-wallet", "bank", "transfer", "keluar"],
    "intent": "Pengguna mencari maklumat mengenai cara mengeluarkan atau menarik balik wang yang diperoleh daripada aplikasi pemanduan, sama ada ia merujuk kepada pendapatan harian, mingguan, atau bonus. Pengguna mungkin ingin tahu tentang proses, tempoh masa, kaedah pembayaran yang tersedia, atau sebarang caj yang dikenakan.",
    "suggestions": [
        "Cara mengeluarkan wang dari akaun pemandu",
        "Tempoh masa pengeluaran pendapatan pemandu",
        "Kaedah pembayaran yang disokong untuk pengeluaran",
        "Bagaimana untuk menukar kaedah pengeluaran",
        "Masalah pengeluaran wang tidak diterima"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(13), intent: 'Pengguna mencari maklumat mengenai cara mengeluark… yang tersedia, atau sebarang caj yang dikenakan.', suggestions: Array(5)}
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："withdrawal"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关...
gemini-assistant.js:807 使用自适应超时: 第2次尝试, 超时6000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
gemini-assistant.js:82 API超时，尝试第2次重试...
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'u4-uaO3sCJX7qtsPh4bZOQ'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": ["akaun", "profil pemandu", "pengurusan akaun", "kemas kini akaun", "butiran akaun", "login akaun", "daftar akaun", "pengesahan akaun", "tetapan akaun", "penyalahgunaan akaun"],
    "intent": "Pengguna mencari maklumat berkaitan dengan akaun pemandu mereka dalam aplikasi atau platform pemanduan. Ini mungkin termasuk cara mendaftar, menguruskan profil, mengemas kini butiran, menyelesaikan masalah log masuk, atau memahami tetapan akaun yang berkaitan dengan operasi pemanduan.",
    "suggestions": [
        "Cara mendaftar akaun pemandu baharu",
        "Bagaimana untuk mengemas kini maklumat profil saya?",
        "Apa yang perlu dilakukan jika saya terlupa kata laluan akaun saya?",
        "Bagaimana untuk menguruskan tetapan privasi akaun saya?",
        "Cara memautkan akaun bank saya untuk pembayaran"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(10), intent: 'Pengguna mencari maklumat berkaitan dengan akaun p…an akaun yang berkaitan dengan operasi pemanduan.', suggestions: Array(5)}
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："account"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）...
gemini-assistant.js:807 使用自适应超时: 第2次尝试, 超时6000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
gemini-assistant.js:82 API超时，尝试第2次重试...
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'u4-uaIvOEumImtkP9_7K4Qo'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": ["kenderaan", "kereta", "moto", "lori", "van", "pendaftaran kenderaan", "lesen memandu", "insurans kenderaan", "servis kenderaan", "penyelenggaraan kenderaan", "jenis kenderaan", "syarat kenderaan", "dokumen kenderaan", "pengangkutan", "penggerak"],
    "intent": "Pengguna mencari maklumat umum mengenai kenderaan, kemungkinan besar berkaitan dengan penggunaan kenderaan untuk tujuan pemanduan profesional (seperti pemandu e-hailing, penghantaran, atau teksi). Ini boleh merangkumi pendaftaran kenderaan, jenis kenderaan yang dibenarkan, syarat-syarat kenderaan, atau penyelenggaraan kenderaan.",
    "suggestions": [
        "Pendaftaran kenderaan untuk pemandu",
        "Jenis kenderaan yang diterima oleh aplikasi pemanduan",
        "Syarat-syarat kenderaan untuk lesen memandu",
        "Cara memeriksa status pendaftaran kenderaan",
        "Insurans kenderaan untuk pemandu profesional",
        "Penjagaan dan penyelenggaraan kenderaan untuk pemandu"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(15), intent: 'Pengguna mencari maklumat umum mengenai kenderaan,…syarat kenderaan, atau penyelenggaraan kenderaan.', suggestions: Array(6)}
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'u4-uaJOTFMC6qtsPsNH3sAU'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": [
        "perkhidmatan pelanggan",
        "khidmat pelanggan",
        "bantuan pelanggan",
        "sokongan pelanggan",
        "soalan lazim pemandu",
        "cara menghubungi sokongan",
        "masalah akaun pemandu",
        "isu aplikasi pemandu",
        "pertanyaan pembayaran pemandu",
        "aduan pemandu",
        "maklum balas pemandu"
    ],
    "intent": "Pengguna mencari maklumat mengenai perkhidmatan pelanggan yang ditawarkan kepada pemandu, termasuk cara mendapatkan bantuan, menyelesaikan masalah berkaitan akaun atau aplikasi, pertanyaan mengenai pembayaran, atau untuk memberikan maklum balas dan membuat aduan.",
    "suggestions": [
        "Cara menghubungi sokongan pelanggan untuk pemandu",
        "Soalan lazim mengenai penggunaan aplikasi pemandu",
        "Penyelesaian masalah akaun pemandu",
        "Cara membuat aduan atau memberikan maklum balas sebagai pemandu",
        "Maklumat mengenai proses pembayaran pemandu"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(11), intent: 'Pengguna mencari maklumat mengenai perkhidmatan pe… untuk memberikan maklum balas dan membuat aduan.', suggestions: Array(5)}
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'u4-uaOqTFbLyqtsP1vqO6A0'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": [
        "cara terima pesanan",
        "cara ambil pesanan",
        "cara dapatkan pesanan",
        "cara menerima tempahan",
        "cara menerima order",
        "cara guna aplikasi untuk pesanan",
        "panduan terima pesanan pemandu",
        "tips terima pesanan",
        "cara menerima pesanan Grab",
        "cara menerima pesanan FoodPanda",
        "cara menerima pesanan Gojek"
    ],
    "intent": "Pengguna mencari panduan atau langkah-langkah tentang bagaimana seorang pemandu (kemungkinan besar pemandu aplikasi penghantaran makanan, barangan, atau teksi) boleh menerima pesanan melalui aplikasi yang digunakan.",
    "suggestions": [
        "Cara mendaftar sebagai pemandu aplikasi penghantaran",
        "Cara menggunakan aplikasi pemandu untuk menerima dan mengurus pesanan",
        "Tips untuk meningkatkan kadar penerimaan pesanan",
        "Cara mengendalikan pesanan yang dibatalkan",
        "Cara membuat pembayaran dan menerima bayaran sebagai pemandu"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(11), intent: 'Pengguna mencari panduan atau langkah-langkah tent…menerima pesanan melalui aplikasi yang digunakan.', suggestions: Array(5)}
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："vehicle"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）...
gemini-assistant.js:807 使用自适应超时: 第2次尝试, 超时6000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
gemini-assistant.js:82 API超时，尝试第2次重试...
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："customer service"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括...
gemini-assistant.js:807 使用自适应超时: 第2次尝试, 超时6000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
gemini-assistant.js:82 API超时，尝试第2次重试...
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："cara menerima pesanan"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关...
gemini-assistant.js:807 使用自适应超时: 第2次尝试, 超时6000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
gemini-assistant.js:82 API超时，尝试第2次重试...
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："pengeluaran"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相...
gemini-assistant.js:807 使用自适应超时: 第2次尝试, 超时6000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'u4-uaL7AMc2hqtsP3rS6-Ao'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": [
        "pengeluaran",
        "kos",
        "belanja",
        "bayaran",
        "gaji",
        "pendapatan",
        "kos operasi",
        "kos pemanduan",
        "kos bahan api",
        "kos penyelenggaraan",
        "kos insurans",
        "kos cukai jalan",
        "pengeluaran tunai",
        "pengeluaran wang",
        "pengeluaran akaun",
        "pengeluaran komisen",
        "pengeluaran bonus",
        "pengeluaran pendapatan harian",
        "pengeluaran pendapatan mingguan",
        "pengeluaran pendapatan bulanan"
    ],
    "intent": "Pengguna mencari maklumat berkaitan dengan pengeluaran wang atau kos yang berkaitan dengan aktiviti pemanduan, sama ada untuk perbelanjaan peribadi, kos operasi kenderaan, atau pengeluaran pendapatan yang diperolehi daripada platform pemanduan.",
    "suggestions": [
        "Cara mengeluarkan pendapatan dari aplikasi pemanduan",
        "Kos penyelenggaraan kereta untuk pemandu",
        "Pengurusan kewangan untuk pemandu e-hailing",
        "Cara mengira kos bahan api untuk pemanduan",
        "Penyata pengeluaran pendapatan pemandu"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(20), intent: 'Pengguna mencari maklumat berkaitan dengan pengelu…atan yang diperolehi daripada platform pemanduan.', suggestions: Array(5)}
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'u4-uaPvCMqG8qtsPr6Sp6QU'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": ["akaun pemandu", "profil pemandu", "daftar akaun", "pengurusan akaun", "login akaun", "kemaskini akaun", "akaun aplikasi", "akaun peribadi"],
    "intent": "Pengguna mencari maklumat berkaitan dengan akaun mereka sebagai pemandu, kemungkinan besar untuk aplikasi pemanduan. Ini boleh merangkumi cara mendaftar, menguruskan profil, log masuk, mengemas kini maklumat, atau memahami fungsi akaun dalam aplikasi.",
    "suggestions": [
        "Cara mendaftar akaun pemandu baru",
        "Bagaimana untuk log masuk ke akaun pemandu saya",
        "Cara mengemas kini maklumat profil pemandu",
        "Masalah akaun pemandu tidak dapat diakses",
        "Pengurusan akaun untuk aplikasi pemanduan"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(8), intent: 'Pengguna mencari maklumat berkaitan dengan akaun m…lumat, atau memahami fungsi akaun dalam aplikasi.', suggestions: Array(5)}
gemini-assistant.js:82 API超时，尝试第2次重试...
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："akaun"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）
2...
gemini-assistant.js:807 使用自适应超时: 第2次尝试, 超时6000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："kenderaan"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术...
gemini-assistant.js:807 使用自适应超时: 第2次尝试, 超时6000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'vI-uaN3yB7myqtsP8b-T0QU'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": [
        "khidmat pelanggan",
        "khidmat pelanggan",
        "customer service",
        "bantuan pelanggan",
        "support pelanggan",
        "masalah pelanggan",
        "pertanyaan pelanggan",
        "cara menghubungi",
        "cara guna aplikasi",
        "isu akaun",
        "masalah pembayaran",
        "penilaian pemandu",
        "keselamatan pemandu",
        "syarat perkhidmatan"
    ],
    "intent": "Pengguna mencari maklumat atau bantuan berkaitan perkhidmatan pelanggan untuk pemandu, berkemungkinan besar berkaitan dengan aplikasi pemanduan, isu akaun, pembayaran, pesanan, atau masalah teknikal.",
    "suggestions": [
        "Cara menghubungi khidmat pelanggan untuk pemandu",
        "Soalan Lazim (FAQ) untuk pemandu",
        "Cara menyelesaikan masalah akaun pemandu",
        "Bantuan penggunaan aplikasi pemandu",
        "Prosedur pembayaran dan pengeluaran pendapatan pemandu",
        "Cara melaporkan isu keselamatan sebagai pemandu",
        "Cara menguruskan penilaian pelanggan"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(14), intent: 'Pengguna mencari maklumat atau bantuan berkaitan p…kaun, pembayaran, pesanan, atau masalah teknikal.', suggestions: Array(7)}
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'vI-uaMGQELWLqtsP6u3R0Ao'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": [
        "kenderaan",
        "kereta",
        "motorsikal",
        "kenderaan persendirian",
        "kenderaan komersial",
        "lesen memandu",
        "pendaftaran kenderaan",
        "insurans kenderaan",
        "penyelenggaraan kenderaan",
        "keselamatan kenderaan",
        "jenis kenderaan",
        "pengangkutan",
        "pengangkutan awam",
        "kenderaan pandu sendiri"
    ],
    "intent": "Pengguna mencari maklumat umum mengenai kenderaan, kemungkinan besar berkaitan dengan penggunaan kenderaan untuk tujuan pemanduan profesional (seperti pemandu e-hailing atau penghantaran), atau mungkin berkaitan dengan pendaftaran, penyelenggaraan, dan keselamatan kenderaan yang mereka gunakan sebagai pemandu.",
    "suggestions": [
        "Cara mendaftar kenderaan untuk aplikasi pemanduan",
        "Jenis kenderaan yang diterima untuk perkhidmatan e-hailing",
        "Peraturan keselamatan kenderaan untuk pemandu",
        "Cara menguruskan pembayaran dan komisen kenderaan",
        "Cara mendapatkan atau memperbaharui lesen memandu",
        "Tips penyelenggaraan kenderaan untuk pemandu",
        "Cara menggunakan aplikasi pemanduan dengan kenderaan anda",
        "Penilaian kenderaan dan impaknya pada pesanan"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(14), intent: 'Pengguna mencari maklumat umum mengenai kenderaan,…an kenderaan yang mereka gunakan sebagai pemandu.', suggestions: Array(8)}
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："khidmat pelanggan"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包...
gemini-assistant.js:807 使用自适应超时: 第2次尝试, 超时6000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'vY-uaMjmFdumqtsP7JKbwAU'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": ["keluar wang", "pengeluaran tunai", "tarik balik", "duit", "akaun bank", "dompet digital", "pembayaran", "pendapatan"],
    "intent": "Pemandu mencari cara untuk mengeluarkan wang atau pendapatan mereka yang terkumpul melalui aplikasi pemandu.",
    "suggestions": ["Cara mengeluarkan pendapatan saya?", "Bagaimana cara menukar mata wang?", "Berapa lama masa untuk pengeluaran?", "Kaedah pengeluaran yang tersedia", "Masalah pengeluaran wang"]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(8), intent: 'Pemandu mencari cara untuk mengeluarkan wang atau …n mereka yang terkumpul melalui aplikasi pemandu.', suggestions: Array(5)}
gemini-assistant.js:60 Gemini enhancement successful in 3039ms: {keywords: Array(8), intent: 'Pemandu mencari cara untuk mengeluarkan wang atau …n mereka yang terkumpul melalui aplikasi pemandu.', suggestions: Array(5)}
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'vY-uaO3pJdajqtsPr5y08AU'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": ["评分", "评价", "分数", "星级", "好评", "差评", "司机评分", "乘客评价", "服务评分", "订单评分", "平台评分"],
    "intent": "用户可能想了解如何查看自己的司机评分、如何提高评分、评分对司机有什么影响，或者想了解乘客如何评价司机。",
    "suggestions": [
        "如何查看我的司机评分？",
        "如何提高我的司机评分？",
        "乘客评价对司机有什么影响？",
        "司机评分标准是什么？",
        "如何处理差评？"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(11), intent: '用户可能想了解如何查看自己的司机评分、如何提高评分、评分对司机有什么影响，或者想了解乘客如何评价司机。', suggestions: Array(5)}
gemini-assistant.js:60 Gemini enhancement successful in 3116ms: {keywords: Array(11), intent: '用户可能想了解如何查看自己的司机评分、如何提高评分、评分对司机有什么影响，或者想了解乘客如何评价司机。', suggestions: Array(5)}
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'vY-uaK6mKeGmqtsPxoOQ0AU'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": ["akaun", "pengguna", "daftar", "login", "profil", "pengurusan akaun", "maklumat pemandu", "akses aplikasi", "kod akaun"],
    "intent": "Pengguna mencari maklumat berkaitan dengan akaun mereka sebagai pemandu, kemungkinan besar berkaitan dengan pendaftaran, log masuk, pengurusan profil, atau akses kepada aplikasi pemandu.",
    "suggestions": [
        "Cara mendaftar akaun pemandu",
        "Bagaimana untuk log masuk ke aplikasi pemandu",
        "Cara menguruskan profil akaun pemandu",
        "Apa yang perlu dilakukan jika terlupa kata laluan akaun",
        "Cara mengaitkan akaun bank untuk pembayaran",
        "Masalah akses akaun aplikasi pemandu"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(9), intent: 'Pengguna mencari maklumat berkaitan dengan akaun m…rusan profil, atau akses kepada aplikasi pemandu.', suggestions: Array(6)}
gemini-assistant.js:60 Gemini enhancement successful in 3211ms: {keywords: Array(9), intent: 'Pengguna mencari maklumat berkaitan dengan akaun m…rusan profil, atau akses kepada aplikasi pemandu.', suggestions: Array(6)}
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'vY-uaMuZN9W9qtsP_fXSsQU'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": ["khidmat pelanggan", "sokongan pelanggan", "bantuan pemandu", "masalah akaun", "pertanyaan aplikasi", "isu pesanan", "bayaran", "ulasan", "kenderaan", "keselamatan"],
    "intent": "Pengguna mencari maklumat atau bantuan berkaitan perkhidmatan pelanggan untuk pemandu, kemungkinan besar berkaitan dengan penggunaan aplikasi, pengurusan akaun, pesanan, pembayaran, atau isu-isu lain yang dihadapi semasa memandu.",
    "suggestions": [
        "Cara menghubungi khidmat pelanggan",
        "Soalan lazim (FAQ) untuk pemandu",
        "Penyelesaian masalah aplikasi pemandu",
        "Cara melaporkan isu pesanan",
        "Maklumat tentang pembayaran dan pengeluaran",
        "Panduan keselamatan untuk pemandu"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(10), intent: 'Pengguna mencari maklumat atau bantuan berkaitan p…, atau isu-isu lain yang dihadapi semasa memandu.', suggestions: Array(6)}
gemini-assistant.js:60 Gemini enhancement successful in 3190ms: {keywords: Array(10), intent: 'Pengguna mencari maklumat atau bantuan berkaitan p…, atau isu-isu lain yang dihadapi semasa memandu.', suggestions: Array(6)}
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'vo-uaPisCcD7qtsPjKDtyAo'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": [
        "cara terima tempahan",
        "cara dapatkan pesanan",
        "terima pesanan pemandu",
        "aplikasi pemandu terima tempahan",
        "panduan terima pesanan",
        "cara menerima pelanggan",
        "proses pesanan pemandu",
        "dapat pesanan baru",
        "urus tempahan pemandu",
        "terima kerja pemandu"
    ],
    "intent": "Pengguna ingin mengetahui langkah-langkah dan cara yang betul untuk menerima pesanan atau tempahan melalui aplikasi pemandu. Ini mungkin termasuk cara mengaktifkan penerimaan pesanan, memahami notifikasi pesanan, dan cara menerima atau menolak pesanan yang masuk.",
    "suggestions": [
        "Cara aktifkan notifikasi pesanan di aplikasi pemandu",
        "Panduan menerima dan menolak pesanan",
        "Apa yang perlu dilakukan selepas menerima pesanan",
        "Cara menguruskan beberapa pesanan serentak",
        "Tips untuk meningkatkan kadar penerimaan pesanan"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(10), intent: 'Pengguna ingin mengetahui langkah-langkah dan cara…an cara menerima atau menolak pesanan yang masuk.', suggestions: Array(5)}
gemini-assistant.js:60 Gemini enhancement successful in 3259ms: {keywords: Array(10), intent: 'Pengguna ingin mengetahui langkah-langkah dan cara…an cara menerima atau menolak pesanan yang masuk.', suggestions: Array(5)}
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'vo-uaKfxDMbAqtsP7O2koAU'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": [
        "kenderaan",
        "kereta",
        "motor",
        "lesen memandu",
        "pendaftaran kenderaan",
        "insurans kenderaan",
        "servis kenderaan",
        "penyelenggaraan kenderaan",
        "jenis kenderaan",
        "syarat kenderaan",
        "dokumen kenderaan",
        "pengangkutan",
        "penggerak",
        "pengangkutan awam",
        "kenderaan persendirian"
    ],
    "intent": "Pengguna mencari maklumat umum mengenai kenderaan, kemungkinan besar berkaitan dengan penggunaan kenderaan untuk tujuan memandu (seperti pemandu teksi, penghantaran, atau perkhidmatan perkongsian perjalanan). Ini boleh merangkumi jenis kenderaan yang dibenarkan, keperluan pendaftaran, insurans, atau penyelenggaraan.",
    "suggestions": [
        "Jenis kenderaan yang dibenarkan untuk aplikasi pemandu",
        "Cara mendaftar kenderaan saya untuk aplikasi pemandu",
        "Insurans kenderaan untuk pemandu aplikasi",
        "Keperluan keselamatan kenderaan untuk pemandu",
        "Penyelenggaraan kenderaan yang disyorkan untuk pemandu"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(15), intent: 'Pengguna mencari maklumat umum mengenai kenderaan,…luan pendaftaran, insurans, atau penyelenggaraan.', suggestions: Array(5)}
gemini-assistant.js:60 Gemini enhancement successful in 3645ms: {keywords: Array(15), intent: 'Pengguna mencari maklumat umum mengenai kenderaan,…luan pendaftaran, insurans, atau penyelenggaraan.', suggestions: Array(5)}
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'vo-uaOe9IqjZqtsPja-ksAw'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": ["pengeluaran", "tarik balik", "tunai", "duit", "bayaran", "gaji", "pendapatan", "komisen", "bonus", "pembayaran", "akaun bank", "dompet digital", "e-wallet", "pengurusan wang", "pengeluaran tunai", "cara keluarkan duit", "syarat pengeluaran", "had pengeluaran", "kaedah pengeluaran", "status pengeluaran"],
    "intent": "Pengguna mencari maklumat mengenai cara mengeluarkan wang atau pendapatan yang diperolehi melalui aplikasi pemanduan. Ini mungkin termasuk pengeluaran gaji, komisen, bonus, atau sebarang bentuk bayaran lain yang berkaitan dengan perkhidmatan pemanduan mereka. Pengguna mungkin ingin mengetahui kaedah, syarat, had, atau status pengeluaran.",
    "suggestions": [
        "Cara membuat pengeluaran pendapatan dari aplikasi pemandu",
        "Syarat dan had pengeluaran tunai untuk pemandu",
        "Kaedah pengeluaran yang disokong (bank transfer, e-wallet)",
        "Cara menyemak status pengeluaran anda",
        "Apa perlu dilakukan jika pengeluaran gagal"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(20), intent: 'Pengguna mencari maklumat mengenai cara mengeluark…hui kaedah, syarat, had, atau status pengeluaran.', suggestions: Array(5)}
gemini-assistant.js:60 Gemini enhancement successful in 3509ms: {keywords: Array(20), intent: 'Pengguna mencari maklumat mengenai cara mengeluark…hui kaedah, syarat, had, atau status pengeluaran.', suggestions: Array(5)}
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'vo-uaIWYL9iwqtsP096ywAY'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": ["kenderaan", "kereta", "motor", "lori", "van", "lesen memandu", "pendaftaran kenderaan", "insurans kenderaan", "servis kenderaan", "penyelenggaraan kenderaan", "jenis kenderaan", "syarat kenderaan", "dokumen kenderaan"],
    "intent": "Pengguna mencari maklumat umum mengenai kenderaan, kemungkinan besar berkaitan dengan penggunaan kenderaan untuk tujuan memandu profesional (seperti pemandu e-hailing, penghantaran) atau keperluan berkaitan pemilikan dan pengurusan kenderaan.",
    "suggestions": [
        "Cara mendaftar kenderaan untuk aplikasi pemandu",
        "Jenis kenderaan yang diterima untuk perkhidmatan penghantaran",
        "Dokumen yang diperlukan untuk kenderaan pemandu",
        "Tips keselamatan memandu kenderaan",
        "Cara menguruskan insurans kenderaan sebagai pemandu profesional"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(13), intent: 'Pengguna mencari maklumat umum mengenai kenderaan,…uan berkaitan pemilikan dan pengurusan kenderaan.', suggestions: Array(5)}
gemini-assistant.js:60 Gemini enhancement successful in 3415ms: {keywords: Array(13), intent: 'Pengguna mencari maklumat umum mengenai kenderaan,…uan berkaitan pemilikan dan pengurusan kenderaan.', suggestions: Array(5)}
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'vo-uaP75N4uDmtkPyZXz2Ao'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": [
        "perkhidmatan pelanggan",
        "khidmat pelanggan",
        "bantuan pelanggan",
        "soalan lazim pemandu",
        "support pemandu",
        "masalah akaun",
        "cara guna aplikasi",
        "isu pesanan",
        "bayaran tertunda",
        "ulasan pemandu",
        "penilaian pemandu",
        "kenderaan tidak layak",
        "keselamatan pemandu",
        "hubungi sokongan"
    ],
    "intent": "Pengguna mencari maklumat atau bantuan berkaitan perkhidmatan pelanggan yang ditawarkan kepada pemandu, kemungkinan besar berkaitan dengan isu-isu yang dihadapi semasa menggunakan aplikasi pemandu, menguruskan pesanan, pembayaran, atau memerlukan bantuan teknikal.",
    "suggestions": [
        "Cara menghubungi sokongan pelanggan untuk pemandu",
        "Soalan lazim mengenai penggunaan aplikasi pemandu",
        "Penyelesaian masalah pembayaran pesanan",
        "Cara menguruskan ulasan negatif daripada pelanggan",
        "Prosedur keselamatan semasa memandu"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(14), intent: 'Pengguna mencari maklumat atau bantuan berkaitan p…an, pembayaran, atau memerlukan bantuan teknikal.', suggestions: Array(5)}
gemini-assistant.js:60 Gemini enhancement successful in 3406ms: {keywords: Array(14), intent: 'Pengguna mencari maklumat atau bantuan berkaitan p…an, pembayaran, atau memerlukan bantuan teknikal.', suggestions: Array(5)}
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'v4-uaJe4A8KiqtsP5rTsuQU'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": ["akaun pemandu", "profil pemandu", "daftar akaun", "pengurusan akaun", "login akaun", "kemaskini akaun", "akaun aplikasi", "akaun peribadi"],
    "intent": "Pengguna mencari maklumat berkaitan dengan akaun pemandu, kemungkinan besar untuk mendaftar, menguruskan profil, log masuk, atau mengemaskini maklumat akaun mereka dalam aplikasi pemanduan.",
    "suggestions": [
        "Cara mendaftar akaun pemandu",
        "Cara log masuk ke akaun pemandu",
        "Cara mengemaskini profil pemandu",
        "Masalah akaun pemandu",
        "Pengurusan akaun aplikasi pemandu"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(8), intent: 'Pengguna mencari maklumat berkaitan dengan akaun p…i maklumat akaun mereka dalam aplikasi pemanduan.', suggestions: Array(5)}
gemini-assistant.js:60 Gemini enhancement successful in 3106ms: {keywords: Array(8), intent: 'Pengguna mencari maklumat berkaitan dengan akaun p…i maklumat akaun mereka dalam aplikasi pemanduan.', suggestions: Array(5)}
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'v4-uaOGtFdmymtkPzdCFuQU'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": [
        "pengeluaran",
        "kos",
        "belanja",
        "bayaran",
        "kos operasi",
        "kos pemanduan",
        "kos penghantaran",
        "kos bahan api",
        "kos penyelenggaraan",
        "kos insurans",
        "kos cukai jalan",
        "kos aplikasi",
        "kos komisen",
        "pengeluaran harian",
        "pengeluaran mingguan",
        "pengeluaran bulanan"
    ],
    "intent": "Pengguna mencari maklumat mengenai pelbagai jenis perbelanjaan atau kos yang berkaitan dengan profesion pemandu, sama ada untuk kenderaan, operasi aplikasi, atau kos peribadi yang timbul daripada aktiviti pemanduan.",
    "suggestions": [
        "Cara menguruskan pengeluaran sebagai pemandu Grab",
        "Senarai penuh kos operasi untuk pemandu e-hailing",
        "Bagaimana mengira keuntungan bersih selepas pengeluaran",
        "Tips mengurangkan pengeluaran bahan api",
        "Pengeluaran yang boleh dituntut untuk pemandu"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(16), intent: 'Pengguna mencari maklumat mengenai pelbagai jenis …peribadi yang timbul daripada aktiviti pemanduan.', suggestions: Array(5)}
gemini-assistant.js:60 Gemini enhancement successful in 3560ms: {keywords: Array(16), intent: 'Pengguna mencari maklumat mengenai pelbagai jenis …peribadi yang timbul daripada aktiviti pemanduan.', suggestions: Array(5)}
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'v4-uaPXvGfH5qtsP-5-M6Ao'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": [
        "kenderaan",
        "kereta",
        "motor",
        "bas",
        "lori",
        "kenderaan persendirian",
        "kenderaan komersial",
        "lesen memandu",
        "pendaftaran kenderaan",
        "insurans kenderaan",
        "servis kenderaan",
        "penjagaan kenderaan",
        "keselamatan kenderaan",
        "pengangkutan",
        "pengendali kenderaan"
    ],
    "intent": "Pengguna mencari maklumat umum mengenai kenderaan, kemungkinan besar berkaitan dengan penggunaan atau pengurusan kenderaan dalam konteks pemanduan atau perkhidmatan pengangkutan.",
    "suggestions": [
        "Cara mendaftar kenderaan untuk aplikasi pemanduan",
        "Jenis-jenis kenderaan yang dibenarkan untuk aplikasi pengangkutan",
        "Peraturan keselamatan kenderaan untuk pemandu",
        "Cara menguruskan dokumen kenderaan",
        "Tips penjagaan kenderaan untuk pemandu",
        "Cara membuat pembayaran berkaitan kenderaan",
        "Cara menilai kenderaan dalam aplikasi pemanduan"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(15), intent: 'Pengguna mencari maklumat umum mengenai kenderaan,…konteks pemanduan atau perkhidmatan pengangkutan.', suggestions: Array(7)}
gemini-assistant.js:60 Gemini enhancement successful in 3313ms: {keywords: Array(15), intent: 'Pengguna mencari maklumat umum mengenai kenderaan,…konteks pemanduan atau perkhidmatan pengangkutan.', suggestions: Array(7)}
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'v4-uaOmbLNGRmtkPhIPyyQU'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": [
        "khidmat pelanggan",
        "khidmat pelanggan",
        "customer service",
        "bantuan pelanggan",
        "support pelanggan",
        "masalah pelanggan",
        "pertanyaan pelanggan",
        "aduan pelanggan",
        "panduan pelanggan",
        "cara menghubungi",
        "hubungi kami",
        "soalan lazim (FAQ)",
        "penumpang",
        "pengguna",
        "aplikasi pemandu",
        "penggunaan aplikasi",
        "tempahan",
        "pesanan",
        "bayaran",
        "pembayaran",
        "rating",
        "ulasan",
        "kenderaan",
        "keselamatan",
        "syarat perkhidmatan",
        "polisi",
        "penafian"
    ],
    "intent": "Pengguna mencari maklumat atau bantuan berkaitan perkhidmatan pelanggan untuk pemandu. Ini mungkin termasuk cara menyelesaikan masalah dengan aplikasi, pertanyaan mengenai pembayaran atau pesanan, aduan mengenai penumpang, atau maklumat umum mengenai penggunaan platform sebagai pemandu.",
    "suggestions": [
        "Cara menghubungi khidmat pelanggan pemandu",
        "Soalan lazim (FAQ) untuk pemandu",
        "Masalah dengan aplikasi pemandu",
        "Cara membuat aduan mengenai penumpang",
        "Maklumat mengenai pembayaran pesanan",
        "Panduan penggunaan aplikasi pemandu",
        "Keselamatan pemandu semasa bertugas",
        "Cara menguruskan rating dan ulasan"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(27), intent: 'Pengguna mencari maklumat atau bantuan berkaitan p…mum mengenai penggunaan platform sebagai pemandu.', suggestions: Array(8)}
gemini-assistant.js:60 Gemini enhancement successful in 3436ms: {keywords: Array(27), intent: 'Pengguna mencari maklumat atau bantuan berkaitan p…mum mengenai penggunaan platform sebagai pemandu.', suggestions: Array(8)}
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'wI-uaIz4Lpq4mtkP-Pi-8AY'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": [
        "cara terima pesanan",
        "cara dapat pesanan",
        "cara ambil order",
        "cara menerima tempahan",
        "cara dapat tempahan",
        "cara ambil tempahan",
        "panduan terima pesanan",
        "tips terima pesanan",
        "cara guna aplikasi terima pesanan",
        "cara terima pesanan Grab",
        "cara terima pesanan FoodPanda",
        "cara terima pesanan Lalamove",
        "cara terima pesanan Gojek"
    ],
    "intent": "Pengguna mencari maklumat dan panduan tentang bagaimana seorang pemandu (kemungkinan pemandu aplikasi penghantaran makanan, barangan atau teksi) boleh menerima pesanan atau tempahan melalui aplikasi atau sistem yang digunakan.",
    "suggestions": [
        "Cara mendaftar sebagai pemandu aplikasi penghantaran",
        "Cara menggunakan aplikasi pemandu untuk menerima pesanan",
        "Tips untuk meningkatkan kadar penerimaan pesanan",
        "Cara menguruskan pesanan yang diterima",
        "Masalah biasa semasa menerima pesanan dan penyelesaiannya"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(13), intent: 'Pengguna mencari maklumat dan panduan tentang baga…ahan melalui aplikasi atau sistem yang digunakan.', suggestions: Array(5)}
gemini-assistant.js:60 Gemini enhancement successful in 5129ms: {keywords: Array(13), intent: 'Pengguna mencari maklumat dan panduan tentang baga…ahan melalui aplikasi atau sistem yang digunakan.', suggestions: Array(5)}
gemini-assistant.js:82 API超时，尝试第3次重试...
gemini-assistant.js:43 Gemini prompt: 你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："account"，语言是马来文。

请注意：所有回复请使用 马来文 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）...
gemini-assistant.js:807 使用自适应超时: 第3次尝试, 超时9000ms
gemini-assistant.js:51 Using enhanced API with timeout
gemini-assistant.js:400 Calling Gemini API: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s
gemini-assistant.js:416 Gemini API response: {candidates: Array(1), usageMetadata: {…}, modelVersion: 'gemini-2.5-flash-lite', responseId: 'xI-uaKKeAuGmqtsP0taQ0AU'}
gemini-assistant.js:420 Gemini response text: ```json
{
    "keywords": ["akaun", "profil pemandu", "pengurusan akaun", "kemas kini akaun", "butiran akaun", "log masuk akaun", "daftar akaun", "lupa kata laluan akaun", "status akaun", "pengesahan akaun"],
    "intent": "Pengguna mencari maklumat berkaitan pengurusan akaun mereka sebagai pemandu, termasuk cara mendaftar, mengemas kini butiran, log masuk, atau menyelesaikan masalah berkaitan akaun.",
    "suggestions": [
        "Cara mendaftar akaun pemandu baharu",
        "Bagaimana untuk mengemas kini maklumat profil saya?",
        "Saya terlupa kata laluan akaun saya, apa yang perlu saya lakukan?",
        "Bagaimana untuk menyemak status akaun saya?",
        "Masalah log masuk akaun pemandu"
    ]
}
```
gemini-assistant.js:426 Failed to parse as JSON, trying to extract JSON part
gemini-assistant.js:431 Extracted JSON result: {keywords: Array(10), intent: 'Pengguna mencari maklumat berkaitan pengurusan aka…asuk, atau menyelesaikan masalah berkaitan akaun.', suggestions: Array(5)}
gemini-assistant.js:60 Gemini enhancement successful in 2216ms: {keywords: Array(10), intent: 'Pengguna mencari maklumat berkaitan pengurusan aka…asuk, atau menyelesaikan masalah berkaitan akaun.', suggestions: Array(5)}
