// 智能搜索建议管理器
// 提供上下文感知的搜索建议、学习用户搜索偏好、热门查询推荐

class SmartSuggestionManager {
    constructor(dataManager, i18n, config = {}) {
        this.dataManager = dataManager;
        this.i18n = i18n;
        this.config = {
            maxSuggestions: config.maxSuggestions || 8,
            maxHistory: config.maxHistory || 50,
            popularityWeight: config.popularityWeight || 0.3,
            contextWeight: config.contextWeight || 0.4,
            semanticWeight: config.semanticWeight || 0.3,
            minSearchLength: config.minSearchLength || 2,
            popularQueryThreshold: config.popularQueryThreshold || 3,
            ...config
        };

        // 搜索历史和统计
        this.searchHistory = this.loadSearchHistory();
        this.queryStats = this.loadQueryStats();
        this.contextCache = new Map();
        
        // 当前会话上下文
        this.sessionContext = {
            currentCategory: null,
            recentQueries: [],
            userPreferences: this.loadUserPreferences(),
            searchPatterns: new Map()
        };

        // 预定义的热门查询（按语言分类）
        this.hotQueries = this.initializeHotQueries();
        
        // 同义词和相关词库
        this.semanticMap = this.initializeSemanticMap();
        
        console.log('✅ 智能搜索建议管理器初始化完成');
    }

    // 生成智能搜索建议
    async generateSuggestions(query, context = {}) {
        if (!query || query.length < this.config.minSearchLength) {
            return this.getDefaultSuggestions(context);
        }

        const suggestions = [];
        const currentLang = this.i18n.getCurrentLanguage();
        
        try {
            // 1. 生成基于历史的建议
            const historyBasedSuggestions = this.getHistoryBasedSuggestions(query, currentLang);
            suggestions.push(...historyBasedSuggestions);

            // 2. 生成语义相关建议
            const semanticSuggestions = this.getSemanticSuggestions(query, currentLang);
            suggestions.push(...semanticSuggestions);

            // 3. 生成上下文感知建议
            const contextSuggestions = this.getContextualSuggestions(query, context, currentLang);
            suggestions.push(...contextSuggestions);

            // 4. 生成热门查询建议
            const popularSuggestions = this.getPopularSuggestions(query, currentLang);
            suggestions.push(...popularSuggestions);

            // 5. 生成完成建议（自动补全）
            const completionSuggestions = this.getCompletionSuggestions(query, currentLang);
            suggestions.push(...completionSuggestions);

            // 6. 对建议进行评分和排序
            const scoredSuggestions = this.scoreSuggestions(suggestions, query, context);
            
            // 7. 去重和限制数量
            const finalSuggestions = this.deduplicateAndLimit(scoredSuggestions);
            
            // 8. 记录建议生成统计
            this.recordSuggestionStats(query, finalSuggestions);

            return finalSuggestions;

        } catch (error) {
            console.warn('生成智能建议时出错:', error);
            return this.getFallbackSuggestions(query, currentLang);
        }
    }

    // 获取默认建议（当查询为空时）
    getDefaultSuggestions(context = {}) {
        const currentLang = this.i18n.getCurrentLanguage();
        const suggestions = [];

        // 添加热门查询
        const hotQueries = this.hotQueries[currentLang] || [];
        hotQueries.slice(0, 4).forEach(query => {
            suggestions.push({
                text: query,
                type: 'hot',
                score: 0.8,
                reason: this.i18n.getText('hotQuery') || '热门查询'
            });
        });

        // 添加最近搜索
        const recentQueries = this.getRecentQueries(currentLang).slice(0, 3);
        recentQueries.forEach(query => {
            suggestions.push({
                text: query.query,
                type: 'recent',
                score: 0.7,
                reason: this.i18n.getText('recentSearch') || '最近搜索'
            });
        });

        // 添加分类建议
        if (context.category) {
            const categoryQueries = this.getCategoryPopularQueries(context.category, currentLang);
            categoryQueries.slice(0, 2).forEach(query => {
                suggestions.push({
                    text: query,
                    type: 'category',
                    score: 0.6,
                    reason: this.i18n.getText('categoryPopular') || '分类热门'
                });
            });
        }

        return suggestions.slice(0, this.config.maxSuggestions);
    }

    // 基于搜索历史生成建议
    getHistoryBasedSuggestions(query, language) {
        const suggestions = [];
        const normalizedQuery = this.normalizeQuery(query);
        
        // 查找相似的历史查询
        const similarQueries = this.searchHistory
            .filter(item => 
                item.language === language &&
                item.query.toLowerCase().includes(normalizedQuery.toLowerCase())
            )
            .sort((a, b) => b.frequency - a.frequency)
            .slice(0, 3);

        similarQueries.forEach(item => {
            suggestions.push({
                text: item.query,
                type: 'history',
                score: Math.min(0.9, 0.5 + (item.frequency * 0.1)),
                reason: this.i18n.getText('searchHistory') || '搜索历史',
                frequency: item.frequency
            });
        });

        return suggestions;
    }

    // 生成语义相关建议
    getSemanticSuggestions(query, language) {
        const suggestions = [];
        const normalizedQuery = this.normalizeQuery(query);
        const semanticMap = this.semanticMap[language] || {};

        // 查找同义词和相关词
        Object.keys(semanticMap).forEach(keyword => {
            if (normalizedQuery.includes(keyword) || keyword.includes(normalizedQuery)) {
                const relatedTerms = semanticMap[keyword] || [];
                relatedTerms.forEach(term => {
                    if (term !== query && term.toLowerCase() !== query.toLowerCase()) {
                        suggestions.push({
                            text: term,
                            type: 'semantic',
                            score: 0.6,
                            reason: this.i18n.getText('relatedTerm') || '相关词汇'
                        });
                    }
                });
            }
        });

        return suggestions.slice(0, 3);
    }

    // 生成上下文感知建议
    getContextualSuggestions(query, context, language) {
        const suggestions = [];
        
        // 基于当前分类的建议
        if (context.category) {
            const categoryQuestions = this.dataManager.getQuestionsByCategory(context.category);
            const relevantQuestions = categoryQuestions
                .filter(q => {
                    const title = q.title[language] || q.title.zh || '';
                    const content = q.content[language] || q.content.zh || '';
                    return title.toLowerCase().includes(query.toLowerCase()) ||
                           content.toLowerCase().includes(query.toLowerCase());
                })
                .slice(0, 2);

            relevantQuestions.forEach(q => {
                const title = q.title[language] || q.title.zh || '';
                suggestions.push({
                    text: title,
                    type: 'context',
                    score: 0.7,
                    reason: this.i18n.getText('categoryMatch') || '分类相关'
                });
            });
        }

        // 基于用户偏好的建议
        const userPrefs = this.sessionContext.userPreferences;
        if (userPrefs.favoriteCategories.length > 0) {
            userPrefs.favoriteCategories.forEach(categoryId => {
                const popularInCategory = this.getCategoryPopularQueries(categoryId, language);
                popularInCategory.slice(0, 1).forEach(queryText => {
                    if (queryText.toLowerCase().includes(query.toLowerCase())) {
                        suggestions.push({
                            text: queryText,
                            type: 'preference',
                            score: 0.65,
                            reason: this.i18n.getText('personalizedSuggestion') || '个性化建议'
                        });
                    }
                });
            });
        }

        return suggestions;
    }

    // 获取热门建议
    getPopularSuggestions(query, language) {
        const suggestions = [];
        const popularQueries = this.getPopularQueries(language);
        
        popularQueries
            .filter(item => 
                item.query.toLowerCase().includes(query.toLowerCase()) ||
                query.toLowerCase().includes(item.query.toLowerCase())
            )
            .slice(0, 2)
            .forEach(item => {
                suggestions.push({
                    text: item.query,
                    type: 'popular',
                    score: 0.75,
                    reason: this.i18n.getText('popularQuery') || '热门搜索',
                    popularity: item.count
                });
            });

        return suggestions;
    }

    // 生成自动补全建议
    getCompletionSuggestions(query, language) {
        const suggestions = [];
        const allQuestions = this.dataManager.getAllQuestions();
        
        // 从FAQ标题中找匹配的补全
        const completions = allQuestions
            .map(q => q.title[language] || q.title.zh || '')
            .filter(title => 
                title.toLowerCase().startsWith(query.toLowerCase()) &&
                title.length > query.length
            )
            .slice(0, 3);

        completions.forEach(completion => {
            suggestions.push({
                text: completion,
                type: 'completion',
                score: 0.8,
                reason: this.i18n.getText('autoCompletion') || '自动补全'
            });
        });

        return suggestions;
    }

    // 对建议进行评分
    scoreSuggestions(suggestions, query, context) {
        return suggestions.map(suggestion => {
            let score = suggestion.score || 0.5;
            
            // 基于查询匹配度调整分数
            const matchScore = this.calculateMatchScore(suggestion.text, query);
            score = score * 0.7 + matchScore * 0.3;
            
            // 基于用户偏好调整
            if (this.isUserPreferredType(suggestion.type)) {
                score += 0.1;
            }
            
            // 基于最近使用调整
            if (this.isRecentlyUsed(suggestion.text)) {
                score += 0.15;
            }
            
            // 确保分数在0-1范围内
            score = Math.min(1, Math.max(0, score));
            
            return {
                ...suggestion,
                score
            };
        });
    }

    // 计算查询匹配分数
    calculateMatchScore(suggestion, query) {
        const suggestionLower = suggestion.toLowerCase();
        const queryLower = query.toLowerCase();
        
        // 完全匹配
        if (suggestionLower === queryLower) return 1.0;
        
        // 前缀匹配
        if (suggestionLower.startsWith(queryLower)) return 0.9;
        
        // 包含匹配
        if (suggestionLower.includes(queryLower)) return 0.7;
        
        // 词语匹配
        const suggestionWords = suggestionLower.split(/\s+/);
        const queryWords = queryLower.split(/\s+/);
        let matchingWords = 0;
        
        queryWords.forEach(word => {
            if (suggestionWords.some(sw => sw.includes(word) || word.includes(sw))) {
                matchingWords++;
            }
        });
        
        return (matchingWords / queryWords.length) * 0.6;
    }

    // 去重和限制数量
    deduplicateAndLimit(suggestions) {
        const seen = new Set();
        const unique = suggestions
            .filter(s => {
                const key = s.text.toLowerCase();
                if (seen.has(key)) {
                    return false;
                }
                seen.add(key);
                return true;
            })
            .sort((a, b) => b.score - a.score)
            .slice(0, this.config.maxSuggestions);

        return unique;
    }

    // 记录用户搜索行为
    recordSearch(query, language, results = [], context = {}) {
        // 更新搜索历史
        this.updateSearchHistory(query, language, results.length > 0);
        
        // 更新查询统计
        this.updateQueryStats(query, language);
        
        // 更新用户偏好
        this.updateUserPreferences(query, context, results);
        
        // 更新会话上下文
        this.updateSessionContext(query, context);
        
        // 持久化数据
        this.saveData();
    }

    // 记录建议使用情况
    recordSuggestionUsage(suggestion, query) {
        // 增加建议的使用统计
        const key = `${suggestion.text}_${suggestion.type}`;
        const usage = this.sessionContext.suggestionUsage?.get(key) || { count: 0, lastUsed: 0 };
        usage.count++;
        usage.lastUsed = Date.now();
        
        if (!this.sessionContext.suggestionUsage) {
            this.sessionContext.suggestionUsage = new Map();
        }
        this.sessionContext.suggestionUsage.set(key, usage);
        
        console.log('📊 记录建议使用:', suggestion.text, suggestion.type);
    }

    // 更新搜索历史
    updateSearchHistory(query, language, hasResults) {
        const normalizedQuery = this.normalizeQuery(query);
        const existing = this.searchHistory.find(
            item => item.query === query && item.language === language
        );
        
        if (existing) {
            existing.frequency++;
            existing.lastUsed = Date.now();
            existing.successRate = hasResults ? 
                (existing.successRate + 1) / 2 : 
                existing.successRate * 0.9;
        } else {
            this.searchHistory.unshift({
                query,
                language,
                frequency: 1,
                lastUsed: Date.now(),
                successRate: hasResults ? 1 : 0,
                normalized: normalizedQuery
            });
        }
        
        // 限制历史记录数量
        if (this.searchHistory.length > this.config.maxHistory) {
            this.searchHistory = this.searchHistory.slice(0, this.config.maxHistory);
        }
    }

    // 更新用户偏好
    updateUserPreferences(query, context, results) {
        const prefs = this.sessionContext.userPreferences;
        
        // 更新分类偏好
        if (context.category && results.length > 0) {
            const categoryIndex = prefs.favoriteCategories.indexOf(context.category);
            if (categoryIndex === -1) {
                prefs.favoriteCategories.push(context.category);
            }
        }
        
        // 更新搜索模式
        const queryPattern = this.extractQueryPattern(query);
        if (queryPattern) {
            const patterns = this.sessionContext.searchPatterns;
            const count = patterns.get(queryPattern) || 0;
            patterns.set(queryPattern, count + 1);
        }
    }

    // 初始化热门查询
    initializeHotQueries() {
        return {
            zh: [
                '如何注册司机账号',
                '接单问题',
                '收入提现',
                '客户评价',
                '车辆要求',
                '工作时间',
                'APP使用',
                '订单纠纷'
            ],
            en: [
                'How to register driver account',
                'Order issues',
                'Payment withdrawal',
                'Customer rating',
                'Vehicle requirements',
                'Working hours',
                'App usage',
                'Order disputes'
            ],
            ms: [
                'Cara daftar akaun pemandu',
                'Isu pesanan',
                'Pengeluaran pembayaran',
                'Penilaian pelanggan',
                'Keperluan kenderaan',
                'Waktu bekerja',
                'Penggunaan aplikasi',
                'Pertikaian pesanan'
            ]
        };
    }

    // 初始化语义映射
    initializeSemanticMap() {
        return {
            zh: {
                '注册': ['申请', '开户', '注册账号', '创建账户'],
                '接单': ['派单', '订单', '工作', '任务'],
                '收入': ['工资', '报酬', '收益', '赚钱'],
                '提现': ['取钱', '转账', '出金', '提取'],
                '评价': ['评分', '星级', '反馈', '评论'],
                '车辆': ['汽车', '车子', '座驾', '交通工具'],
                '问题': ['困难', '故障', '错误', '疑问']
            },
            en: {
                'register': ['sign up', 'create account', 'join', 'enroll'],
                'order': ['ride', 'trip', 'booking', 'request'],
                'income': ['earnings', 'payment', 'salary', 'money'],
                'withdraw': ['cash out', 'transfer', 'payout', 'extract'],
                'rating': ['review', 'feedback', 'score', 'stars'],
                'vehicle': ['car', 'automobile', 'transport', 'ride'],
                'problem': ['issue', 'trouble', 'error', 'difficulty']
            },
            ms: {
                'daftar': ['mendaftar', 'membuat akaun', 'sertai', 'mohon'],
                'pesanan': ['perjalanan', 'tempahan', 'permintaan', 'kerja'],
                'pendapatan': ['gaji', 'bayaran', 'wang', 'keuntungan'],
                'keluar': ['tunai keluar', 'pindah', 'ambil', 'keluarkan'],
                'penilaian': ['ulasan', 'maklum balas', 'skor', 'bintang'],
                'kenderaan': ['kereta', 'kenderaan bermotor', 'pengangkutan'],
                'masalah': ['isu', 'kesulitan', 'ralat', 'pertanyaan']
            }
        };
    }

    // 工具方法
    normalizeQuery(query) {
        return query.toLowerCase().trim().replace(/[^\w\s\u4e00-\u9fff]/g, '');
    }

    getRecentQueries(language, limit = 5) {
        return this.searchHistory
            .filter(item => item.language === language)
            .sort((a, b) => b.lastUsed - a.lastUsed)
            .slice(0, limit);
    }

    getPopularQueries(language, limit = 10) {
        return this.searchHistory
            .filter(item => 
                item.language === language && 
                item.frequency >= this.config.popularQueryThreshold
            )
            .sort((a, b) => b.frequency - a.frequency)
            .slice(0, limit);
    }

    getCategoryPopularQueries(categoryId, language) {
        const queries = this.queryStats[`${categoryId}_${language}`] || [];
        return queries
            .sort((a, b) => b.count - a.count)
            .slice(0, 3)
            .map(item => item.query);
    }

    isUserPreferredType(type) {
        const prefs = this.sessionContext.userPreferences;
        return prefs.preferredSuggestionTypes?.includes(type) || false;
    }

    isRecentlyUsed(text) {
        const recentThreshold = 30 * 60 * 1000; // 30分钟
        return this.sessionContext.recentQueries.some(
            item => item.text === text && (Date.now() - item.timestamp) < recentThreshold
        );
    }

    extractQueryPattern(query) {
        // 简单的模式提取（可以扩展为更复杂的NLP分析）
        if (query.includes('如何') || query.includes('怎么')) return 'how-to';
        if (query.includes('什么') || query.includes('是什么')) return 'what-is';
        if (query.includes('为什么')) return 'why';
        if (query.includes('问题') || query.includes('错误')) return 'problem';
        return 'general';
    }

    // 数据持久化
    loadSearchHistory() {
        try {
            const saved = localStorage.getItem('smart-search-history');
            return saved ? JSON.parse(saved) : [];
        } catch (e) {
            console.warn('加载搜索历史失败:', e);
            return [];
        }
    }

    loadQueryStats() {
        try {
            const saved = localStorage.getItem('smart-query-stats');
            return saved ? JSON.parse(saved) : {};
        } catch (e) {
            return {};
        }
    }

    loadUserPreferences() {
        try {
            const saved = localStorage.getItem('smart-user-prefs');
            return saved ? JSON.parse(saved) : {
                favoriteCategories: [],
                preferredSuggestionTypes: ['recent', 'popular', 'completion'],
                searchPreferences: {}
            };
        } catch (e) {
            return {
                favoriteCategories: [],
                preferredSuggestionTypes: ['recent', 'popular', 'completion'],
                searchPreferences: {}
            };
        }
    }

    saveData() {
        try {
            localStorage.setItem('smart-search-history', JSON.stringify(this.searchHistory));
            localStorage.setItem('smart-query-stats', JSON.stringify(this.queryStats));
            localStorage.setItem('smart-user-prefs', JSON.stringify(this.sessionContext.userPreferences));
        } catch (e) {
            console.warn('保存搜索数据失败:', e);
        }
    }

    updateSessionContext(query, context) {
        const session = this.sessionContext;
        session.recentQueries.unshift({
            text: query,
            timestamp: Date.now(),
            context
        });
        
        // 限制最近查询数量
        if (session.recentQueries.length > 10) {
            session.recentQueries = session.recentQueries.slice(0, 10);
        }
        
        if (context.category) {
            session.currentCategory = context.category;
        }
    }

    updateQueryStats(query, language) {
        if (!this.queryStats[language]) {
            this.queryStats[language] = {};
        }
        
        const stats = this.queryStats[language];
        stats[query] = (stats[query] || 0) + 1;
    }

    recordSuggestionStats(query, suggestions) {
        console.log('📊 建议生成统计:', {
            query,
            suggestionsCount: suggestions.length,
            types: suggestions.map(s => s.type),
            avgScore: suggestions.reduce((acc, s) => acc + s.score, 0) / suggestions.length
        });
    }

    getFallbackSuggestions(query, language) {
        // 降级到简单建议
        const hotQueries = this.hotQueries[language] || [];
        return hotQueries.slice(0, 4).map(text => ({
            text,
            type: 'fallback',
            score: 0.5,
            reason: this.i18n.getText('defaultSuggestion') || '默认建议'
        }));
    }

    // 获取建议统计
    getStats() {
        return {
            historySize: this.searchHistory.length,
            queryStatsSize: Object.keys(this.queryStats).length,
            contextCacheSize: this.contextCache.size,
            userPreferences: this.sessionContext.userPreferences,
            recentQueries: this.sessionContext.recentQueries.length
        };
    }

    // 清理过期数据
    cleanup() {
        const now = Date.now();
        const maxAge = 30 * 24 * 60 * 60 * 1000; // 30天
        
        // 清理过期搜索历史
        this.searchHistory = this.searchHistory.filter(
            item => (now - item.lastUsed) < maxAge
        );
        
        // 清理上下文缓存
        this.contextCache.clear();
        
        console.log('🧹 智能建议管理器数据清理完成');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SmartSuggestionManager;
} else {
    window.SmartSuggestionManager = SmartSuggestionManager;
}