// 统一搜索引擎 - 实现即时本地搜索 + AI渐进增强
(function() {
    'use strict';

    // LRU缓存实现
    class LRUCache {
        constructor(maxSize = 1000) {
            this.maxSize = maxSize;
            this.cache = new Map();
        }

        get(key) {
            if (!this.cache.has(key)) return null;
            
            // LRU - 移动到最前面
            const value = this.cache.get(key);
            this.cache.delete(key);
            this.cache.set(key, value);
            return value;
        }

        set(key, value, ttl = 300000) { // 默认5分钟过期
            // 检查是否已存在
            if (this.cache.has(key)) {
                this.cache.delete(key);
            }
            // 检查缓存大小
            else if (this.cache.size >= this.maxSize) {
                // 删除最旧的项目
                const firstKey = this.cache.keys().next().value;
                this.cache.delete(firstKey);
            }

            // 添加过期时间
            const item = {
                value,
                expireTime: Date.now() + ttl
            };
            
            this.cache.set(key, item);
        }

        has(key) {
            const item = this.cache.get(key);
            if (!item) return false;
            
            // 检查是否过期
            if (Date.now() > item.expireTime) {
                this.cache.delete(key);
                return false;
            }
            
            return true;
        }

        clear() {
            this.cache.clear();
        }
    }

    // 搜索性能指标收集
    class SearchMetrics {
        constructor() {
            this.metrics = [];
            this.maxMetrics = 100;

            // 性能预算配置
            this.performanceBudget = {
                totalSearchTarget: 2000,  // 总搜索时间目标：2秒
                localSearchTarget: 500,   // 本地搜索目标：500ms
                aiSearchTarget: 1000,     // AI搜索目标：1秒
                ragSearchTarget: 800      // RAG搜索目标：800ms
            };
        }

        record(data) {
            this.metrics.push({
                ...data,
                timestamp: Date.now()
            });

            // 保持最近100条记录
            if (this.metrics.length > this.maxMetrics) {
                this.metrics.shift();
            }
        }

        getAverage(field) {
            if (this.metrics.length === 0) return 0;
            const sum = this.metrics.reduce((acc, metric) => acc + (metric[field] || 0), 0);
            return sum / this.metrics.length;
        }

        getStats() {
            return {
                count: this.metrics.length,
                avgLocalTime: this.getAverage('localTime'),
                avgAiTime: this.getAverage('aiTime'),
                avgRagTime: this.getAverage('ragTime'),
                avgTotalTime: this.getAverage('totalTime'),
                cacheHitRate: this.getAverage('cacheHit') * 100,
                ragSuccessRate: this.getAverage('ragSuccess') * 100
            };
        }

        // 计算系统健康分数
        calculateHealthScore() {
            if (this.metrics.length === 0) return 1.0;

            const stats = this.getStats();
            let score = 1.0;

            // 基于平均响应时间计算健康度
            if (stats.avgTotalTime > this.performanceBudget.totalSearchTarget) {
                score -= 0.3; // 响应时间超标扣30分
            }

            // 基于成功率计算健康度
            const localSuccessRate = this.getAverage('localSuccess');
            const aiSuccessRate = this.getAverage('aiSuccess');
            const ragSuccessRate = this.getAverage('ragSuccess');

            const overallSuccessRate = (localSuccessRate + aiSuccessRate + ragSuccessRate) / 3;
            score = score * (0.5 + overallSuccessRate * 0.5); // 成功率影响50%权重

            return Math.max(0, Math.min(1, score));
        }
    }

    // 智能结果合并器
    class ResultMerger {
        constructor() {
            this.weights = {
                exactMatch: 1.0,
                titleMatch: 0.9,
                contentMatch: 0.7,
                tagMatch: 0.8,
                aiRelevance: 0.6,
                semanticSimilarity: 0.75,
                vectorMatch: 0.8
            };
        }

        merge(localResults, aiEnhancement) {
            if (!aiEnhancement || !localResults) return localResults || [];

            // 如果AI提供了重新排序的结果
            if (aiEnhancement.rerankedResults) {
                return this.mergeRerankedResults(localResults, aiEnhancement.rerankedResults);
            }

            // 如果AI提供了额外的相关问题
            if (aiEnhancement.additionalResults) {
                return this.mergeAdditionalResults(localResults, aiEnhancement.additionalResults);
            }

            // 默认基于AI评分重新排序
            return this.rerankWithAI(localResults, aiEnhancement);
        }

        rerankWithAI(results, enhancement) {
            return results.map(result => {
                const aiScore = this.calculateAIScore(result, enhancement);
                const combinedScore = (result.score * 0.7) + (aiScore * 0.3);
                
                return {
                    ...result,
                    aiScore,
                    combinedScore,
                    aiEnhanced: true
                };
            }).sort((a, b) => b.combinedScore - a.combinedScore);
        }

        calculateAIScore(result, enhancement) {
            if (!enhancement || !enhancement.keywords) return 0;

            let score = 0;
            const title = (result.title[enhancement.language] || '').toLowerCase();
            const content = (result.content[enhancement.language] || '').toLowerCase();

            // 检查AI关键词匹配
            enhancement.keywords.forEach(keyword => {
                const kw = keyword.toLowerCase();
                if (title.includes(kw)) score += 0.8;
                if (content.includes(kw)) score += 0.6;
            });

            return Math.min(score, 1.0);
        }

        mergeAdditionalResults(localResults, additionalResults) {
            const existingIds = new Set(localResults.map(r => r.id));
            const newResults = additionalResults.filter(r => !existingIds.has(r.id));
            
            return [...localResults, ...newResults]
                .sort((a, b) => (b.combinedScore || b.score) - (a.combinedScore || a.score));
        }

        // 三轨道结果合并 (Local + AI + Vector)
        mergeThreeTrack(localResults, aiEnhancement, ragResults) {
            if (!localResults) localResults = [];
            
            // 如果没有AI或RAG结果，返回本地结果
            if (!aiEnhancement && !ragResults) {
                return localResults;
            }

            // 如果只有RAG结果，合并RAG和本地结果
            if (!aiEnhancement && ragResults) {
                return this.mergeRAGResults(localResults, ragResults.results || ragResults);
            }

            // 如果只有AI结果，使用现有的merge方法
            if (aiEnhancement && !ragResults) {
                return this.merge(localResults, aiEnhancement);
            }

            // 三重合并：首先合并本地和AI，然后加入RAG结果
            let combinedResults = this.merge(localResults, aiEnhancement);
            const ragResultsArray = ragResults.results || ragResults || [];
            
            if (ragResultsArray.length > 0) {
                combinedResults = this.mergeRAGResults(combinedResults, ragResultsArray);
            }

            return this.finalRanking(combinedResults);
        }

        // 合并RAG向量搜索结果
        mergeRAGResults(existingResults, ragResults) {
            if (!ragResults || ragResults.length === 0) {
                return existingResults;
            }

            const resultMap = new Map();
            const existingScores = new Map();

            // 添加现有结果
            existingResults.forEach(result => {
                resultMap.set(result.id, result);
                existingScores.set(result.id, result.combinedScore || result.score || 0);
            });

            // 处理RAG结果
            ragResults.forEach(ragResult => {
                const id = ragResult.id;
                const vectorScore = ragResult.similarity || ragResult.score || 0;
                const boostFactor = this.weights.semanticSimilarity;

                if (resultMap.has(id)) {
                    // 已存在的结果 - 提升分数
                    const existing = resultMap.get(id);
                    const existingScore = existingScores.get(id);
                    const boostedScore = existingScore + (vectorScore * boostFactor);
                    
                    resultMap.set(id, {
                        ...existing,
                        combinedScore: boostedScore,
                        vectorMatch: true,
                        vectorScore: vectorScore,
                        semanticRelevance: vectorScore,
                        sources: [...(existing.sources || []), 'vector']
                    });
                } else {
                    // 新的向量结果
                    resultMap.set(id, {
                        ...ragResult,
                        combinedScore: vectorScore * this.weights.vectorMatch,
                        vectorMatch: true,
                        vectorScore: vectorScore,
                        semanticRelevance: vectorScore,
                        matchType: 'semantic',
                        source: 'vector',
                        sources: ['vector']
                    });
                }
            });

            return Array.from(resultMap.values())
                .sort((a, b) => (b.combinedScore || b.score) - (a.combinedScore || a.score))
                .slice(0, 25); // 限制最大结果数
        }

        // 最终排序和优化
        finalRanking(results) {
            return results.map(result => {
                // 计算最终综合分数
                let finalScore = result.combinedScore || result.score || 0;
                
                // 向量匹配加成
                if (result.vectorMatch) {
                    finalScore += (result.vectorScore || 0) * 0.1;
                }

                // AI增强加成
                if (result.aiEnhanced) {
                    finalScore += (result.aiScore || 0) * 0.1;
                }

                // 多源匹配加成
                if (result.sources && result.sources.length > 1) {
                    finalScore *= 1.15; // 15% 提升
                }

                return {
                    ...result,
                    finalScore,
                    multiSource: result.sources && result.sources.length > 1
                };
            })
            .sort((a, b) => (b.finalScore || b.combinedScore || b.score) - 
                            (a.finalScore || a.combinedScore || a.score))
            .slice(0, 20); // 最终限制为20个结果
        }
    }

    // 统一搜索引擎主类
    class UnifiedSearchEngine {
        constructor(dataManager, geminiAssistant, i18nManager) {
            this.dataManager = dataManager;
            this.geminiAssistant = geminiAssistant;
            this.i18n = i18nManager;
            
            // 缓存系统
            this.localCache = new LRUCache(1000);
            this.aiCache = new LRUCache(500);
            
            // 性能监控
            this.metrics = new SearchMetrics();
            
            // 结果合并器
            this.merger = new ResultMerger();
            
            // 活跃搜索管理
            this.activeSearches = new Map();
            
            // 防抖定时器
            this.debounceTimer = null;
            
            // RAG向量搜索引擎
            this.ragEngine = null;
            this.ragInitialized = false;
            
            console.log('UnifiedSearchEngine initialized');
            
            // 异步初始化RAG引擎
            this.initializeRAGEngine();
        }

        // 高性能RAG向量搜索引擎初始化
        async initializeRAGEngine() {
            try {
                if (typeof RAGVectorEngine === 'undefined') {
                    console.warn('RAGVectorEngine未加载，跳过向量搜索初始化');
                    return;
                }

                console.log('初始化高性能RAG向量搜索引擎...');
                this.ragEngine = new RAGVectorEngine({
                    maxResults: 15,
                    similarityThreshold: 0.05, // 降低阈值从0.15到0.05提高结果覆盖率
                    enableCache: true,
                    cacheSize: 100,
                    enableParallelProcessing: true,
                    chunkSize: 50,
                    enableMemoryOptimization: true,
                    vectorCompressionLevel: 0.01
                });

                // 从DataManager获取FAQ数据
                const faqData = {
                    questions: this.dataManager.data.questions
                };

                const result = await this.ragEngine.initializeFromFAQData(faqData);
                
                if (result.success) {
                    this.ragInitialized = true;
                    console.log('✅ RAG向量搜索引擎初始化成功:', result);
                } else {
                    console.warn('⚠️ RAG向量搜索引擎初始化失败:', result.error);
                }

            } catch (error) {
                console.error('RAG引擎初始化出错:', error);
            }
        }

        // 主搜索入口 - 三轨道并行处理 (Local + AI + Vector)
        async search(query, language = null, callbacks = {}) {
            const startTime = Date.now();
            const currentLanguage = language || this.i18n.getCurrentLanguage();
            const searchId = this.generateSearchId(query, currentLanguage);
            
            // 清理输入
            const normalizedQuery = this.normalizeQuery(query);
            if (normalizedQuery.length < 2) {
                callbacks.onComplete?.([]);
                return [];
            }

            // 取消之前的搜索
            this.cancelPreviousSearch();

            // 检查缓存 (包含RAG缓存)
            const cacheKey = `${normalizedQuery}:${currentLanguage}`;
            const ragCacheKey = `rag:${normalizedQuery}`;
            const cachedLocal = this.localCache.get(cacheKey);
            const cachedRAG = this.ragInitialized ? this.localCache.get(ragCacheKey) : null;
            
            if (cachedLocal) {
                let results = cachedLocal.value;
                callbacks.onProgress?.({
                    stage: 'cached',
                    results,
                    confidence: 0.9,
                    timestamp: Date.now()
                });

                // 检查AI和RAG缓存
                const cachedAI = this.aiCache.get(cacheKey);
                if (cachedAI && cachedRAG) {
                    // 三重缓存命中 - 合并所有结果
                    const enhancedResults = this.merger.mergeThreeTrack(results, cachedAI.value, cachedRAG.value);
                    callbacks.onComplete?.(enhancedResults);
                    
                    this.metrics.record({
                        localTime: 0,
                        aiTime: 0,
                        ragTime: 0,
                        totalTime: Date.now() - startTime,
                        cacheHit: 1,
                        resultsCount: enhancedResults.length
                    });
                    
                    return enhancedResults;
                } else if (cachedAI) {
                    // 双重缓存命中 (Local + AI)
                    const enhancedResults = this.merger.merge(results, cachedAI.value);
                    // 后台执行RAG搜索
                    this.performBackgroundRAGSearch(normalizedQuery, enhancedResults, callbacks);
                    callbacks.onComplete?.(enhancedResults);
                    return enhancedResults;
                } else {
                    // 只有本地缓存，后台触发AI和RAG增强
                    this.performBackgroundEnhancements(normalizedQuery, currentLanguage, results, callbacks);
                    callbacks.onComplete?.(results);
                    return results;
                }
            }

            // 记录搜索
            this.activeSearches.set(searchId, {
                query: normalizedQuery,
                language: currentLanguage,
                startTime,
                cancelled: false
            });

            try {
                // 并行启动三个轨道
                const searchPromises = [
                    this.performLocalSearch(normalizedQuery, currentLanguage, callbacks, searchId),
                    this.performAIEnhancement(normalizedQuery, currentLanguage, callbacks, searchId)
                ];

                // 添加RAG搜索轨道（如果可用）
                if (this.ragInitialized) {
                    searchPromises.push(
                        this.performRAGSearch(normalizedQuery, currentLanguage, callbacks, searchId)
                    );
                }

                const searchResults = await Promise.allSettled(searchPromises);
                const [localResults, aiEnhancement, ragResults] = searchResults;

                // 检查搜索是否被取消
                const search = this.activeSearches.get(searchId);
                if (!search || search.cancelled) {
                    return [];
                }

                // 合并结果
                const finalResults = this.mergeThreeTrackResults(
                    localResults, aiEnhancement, ragResults, searchId
                );
                
                // 缓存结果
                this.cacheThreeTrackResults(
                    cacheKey, ragCacheKey, 
                    localResults.value || [], aiEnhancement.value, ragResults?.value
                );
                
                // 性能记录
                this.recordThreeTrackMetrics(startTime, localResults, aiEnhancement, ragResults, finalResults);
                
                // 完成回调
                callbacks.onComplete?.(finalResults);
                
                return finalResults;

            } catch (error) {
                console.error('Search failed:', error);
                callbacks.onError?.(error);
                return [];
            } finally {
                // 清理活跃搜索记录
                this.activeSearches.delete(searchId);
            }
        }

        // 轨道1: 即时本地搜索
        async performLocalSearch(query, language, callbacks, searchId) {
            const localStartTime = Date.now();

            try {
                // 阶段1: 基础快速搜索 (精确匹配)
                const basicResults = await this.dataManager.quickSearch(query, language);
                
                // 检查是否被取消
                if (this.isSearchCancelled(searchId)) return [];

                callbacks.onProgress?.({
                    stage: 'basic',
                    results: basicResults,
                    confidence: 0.8,
                    timestamp: Date.now(),
                    timing: { local: Date.now() - localStartTime }
                });

                // 阶段2: 模糊匹配增强
                const fuzzyResults = await this.dataManager.fuzzySearch(query, language);
                
                // 检查是否被取消
                if (this.isSearchCancelled(searchId)) return [];

                // 合并和排序结果
                const mergedResults = this.mergeAndRankLocal(basicResults, fuzzyResults);

                callbacks.onProgress?.({
                    stage: 'fuzzy',
                    results: mergedResults,
                    confidence: 0.85,
                    timestamp: Date.now(),
                    timing: { local: Date.now() - localStartTime }
                });

                return mergedResults;

            } catch (error) {
                console.error('Local search failed:', error);
                callbacks.onProgress?.({
                    stage: 'error',
                    results: [],
                    error: error.message,
                    timestamp: Date.now()
                });
                return [];
            }
        }

        // 轨道2: AI增强搜索
        async performAIEnhancement(query, language, callbacks, searchId) {
            if (!this.geminiAssistant || !this.geminiAssistant.isAvailable()) {
                return null;
            }

            const aiStartTime = Date.now();

            try {
                // 2秒超时的AI调用
                const enhancement = await Promise.race([
                    this.geminiAssistant.enhanceQuery(query, language),
                    this.createTimeout(2000)
                ]);

                // 检查是否被取消
                if (this.isSearchCancelled(searchId)) return null;

                callbacks.onProgress?.({
                    stage: 'ai-enhanced',
                    enhancement,
                    confidence: 0.95,
                    timestamp: Date.now(),
                    timing: { ai: Date.now() - aiStartTime }
                });

                return enhancement;

            } catch (error) {
                // 静默失败 - AI失败不应该影响用户体验
                console.debug('AI enhancement failed (non-critical):', error.message);
                return null;
            }
        }

        // 轨道3: RAG向量搜索
        async performRAGSearch(query, language, callbacks, searchId) {
            if (!this.ragInitialized || !this.ragEngine) {
                return null;
            }

            const ragStartTime = Date.now();

            try {
                // 2.5秒超时的RAG调用
                const ragResults = await Promise.race([
                    this.ragEngine.semanticSearch(query, {
                        maxResults: 8,
                        similarityThreshold: 0.05 // 降低实际搜索阈值
                    }),
                    this.createTimeout(2500)
                ]);

                // 检查是否被取消
                if (this.isSearchCancelled(searchId)) return null;

                if (ragResults && ragResults.results && ragResults.results.length > 0) {
                    callbacks.onProgress?.({
                        stage: 'rag-semantic',
                        results: ragResults.results,
                        confidence: 0.85,
                        timestamp: Date.now(),
                        timing: { rag: Date.now() - ragStartTime },
                        vectorMatches: ragResults.results.length
                    });

                    return ragResults;
                }

                return null;

            } catch (error) {
                // 静默失败 - RAG失败不应该影响用户体验
                console.debug('RAG semantic search failed (non-critical):', error.message);
                return null;
            }
        }

        // 后台AI增强（用于缓存场景）
        async performBackgroundAIEnhancement(query, language, localResults, callbacks) {
            try {
                const enhancement = await this.geminiAssistant.enhanceQuery(query, language);
                if (enhancement) {
                    const enhancedResults = this.merger.merge(localResults, enhancement);
                    
                    // 更新缓存
                    const cacheKey = `${query}:${language}`;
                    this.aiCache.set(cacheKey, enhancement);
                    
                    // 通知UI更新
                    callbacks.onProgress?.({
                        stage: 'background-enhanced',
                        results: enhancedResults,
                        confidence: 0.95,
                        timestamp: Date.now()
                    });
                }
            } catch (error) {
                console.debug('Background AI enhancement failed:', error.message);
            }
        }

        // 后台RAG搜索
        async performBackgroundRAGSearch(query, currentResults, callbacks) {
            if (!this.ragInitialized || !this.ragEngine) return;

            try {
                const ragResults = await this.ragEngine.semanticSearch(query, {
                    maxResults: 5,
                    similarityThreshold: 0.05 // 统一降低后台搜索阈值
                });

                if (ragResults && ragResults.results && ragResults.results.length > 0) {
                    // 合并RAG结果与当前结果
                    const enhancedResults = this.merger.mergeRAGResults(currentResults, ragResults.results);
                    
                    // 更新缓存
                    const ragCacheKey = `rag:${query}`;
                    this.localCache.set(ragCacheKey, ragResults);
                    
                    // 通知UI更新
                    callbacks.onProgress?.({
                        stage: 'background-rag',
                        results: enhancedResults,
                        confidence: 0.88,
                        timestamp: Date.now(),
                        vectorMatches: ragResults.results.length
                    });
                }
            } catch (error) {
                console.debug('Background RAG search failed:', error.message);
            }
        }

        // 后台增强（AI + RAG）
        async performBackgroundEnhancements(query, language, localResults, callbacks) {
            // 并行执行AI和RAG增强
            const enhancements = await Promise.allSettled([
                this.performBackgroundAIEnhancement(query, language, localResults, callbacks),
                this.performBackgroundRAGSearch(query, localResults, callbacks)
            ]);

            console.debug(`Background enhancements completed for query: ${query}`);
        }

        // 合并三轨道搜索结果
        mergeThreeTrackResults(localResults, aiEnhancement, ragResults, searchId) {
            if (this.isSearchCancelled(searchId)) return [];

            const local = localResults.status === 'fulfilled' ? localResults.value : [];
            const ai = aiEnhancement.status === 'fulfilled' ? aiEnhancement.value : null;
            const rag = ragResults && ragResults.status === 'fulfilled' ? ragResults.value : null;

            return this.merger.mergeThreeTrack(local, ai, rag);
        }

        // 兼容性方法：合并双轨道搜索结果
        mergeSearchResults(localResults, aiEnhancement, searchId) {
            if (this.isSearchCancelled(searchId)) return [];

            const local = localResults.status === 'fulfilled' ? localResults.value : [];
            const ai = aiEnhancement.status === 'fulfilled' ? aiEnhancement.value : null;

            return this.merger.merge(local, ai);
        }

        // 本地结果合并和排序
        mergeAndRankLocal(basicResults, fuzzyResults) {
            const resultMap = new Map();

            // 添加基础结果（高权重）
            basicResults.forEach(result => {
                resultMap.set(result.id, {
                    ...result,
                    score: (result.score || 0) * 1.2 // 精确匹配加权
                });
            });

            // 添加模糊结果（标准权重）
            fuzzyResults.forEach(result => {
                if (!resultMap.has(result.id)) {
                    resultMap.set(result.id, result);
                } else {
                    // 如果已存在，取更高分数
                    const existing = resultMap.get(result.id);
                    if (result.score > existing.score) {
                        resultMap.set(result.id, result);
                    }
                }
            });

            // 转为数组并排序
            return Array.from(resultMap.values())
                .sort((a, b) => (b.score || 0) - (a.score || 0))
                .slice(0, 20); // 限制前20个结果
        }

        // 三轨道缓存结果
        cacheThreeTrackResults(localKey, ragKey, localResults, aiEnhancement, ragResults) {
            if (localResults && localResults.length > 0) {
                this.localCache.set(localKey, localResults);
            }
            
            if (aiEnhancement) {
                this.aiCache.set(localKey, aiEnhancement);
            }

            if (ragResults && ragResults.results && ragResults.results.length > 0) {
                this.localCache.set(ragKey, ragResults);
            }
        }

        // 兼容性方法：缓存结果
        cacheResults(key, localResults, aiEnhancement) {
            if (localResults && localResults.length > 0) {
                this.localCache.set(key, localResults);
            }
            
            if (aiEnhancement) {
                this.aiCache.set(key, aiEnhancement);
            }
        }

        // 增强三轨道性能指标记录
        recordThreeTrackMetrics(startTime, localResults, aiEnhancement, ragResults, finalResults) {
            const totalTime = Date.now() - startTime;
            const localTime = localResults.status === 'fulfilled' ? 
                (localResults.value?.timing?.local || 0) : 0;
            const aiTime = aiEnhancement.status === 'fulfilled' ? 
                (aiEnhancement.value?.timing?.ai || 0) : 0;
            const ragTime = ragResults && ragResults.status === 'fulfilled' ? 
                (ragResults.value?.searchTime || ragResults.value?.timing?.rag || 0) : 0;

            const metrics = {
                localTime,
                aiTime,
                ragTime,
                totalTime,
                cacheHit: 0,
                resultsCount: finalResults.length,
                localSuccess: localResults.status === 'fulfilled',
                aiSuccess: aiEnhancement.status === 'fulfilled',
                ragSuccess: ragResults ? ragResults.status === 'fulfilled' : false,
                ragEnabled: this.ragInitialized,
                timestamp: Date.now(),
                performanceGrade: this.calculatePerformanceGrade(totalTime, localTime, aiTime, ragTime)
            };
            
            this.metrics.record(metrics);
            
            // 性能预警
            if (totalTime > this.metrics.performanceBudget.totalSearchTarget) {
                console.warn(`搜索性能超过预算: ${totalTime}ms > ${this.metrics.performanceBudget.totalSearchTarget}ms`);
            }
        }
        
        // 性能等级计算
        calculatePerformanceGrade(totalTime, localTime, aiTime, ragTime) {
            const budget = this.metrics.performanceBudget;
            
            if (totalTime <= budget.totalSearchTarget * 0.5) return 'A';
            if (totalTime <= budget.totalSearchTarget * 0.7) return 'B';
            if (totalTime <= budget.totalSearchTarget) return 'C';
            if (totalTime <= budget.totalSearchTarget * 1.5) return 'D';
            return 'F';
        }

        // 兼容性方法：记录性能指标
        recordMetrics(startTime, localResults, aiEnhancement, finalResults) {
            const totalTime = Date.now() - startTime;
            const localTime = localResults.status === 'fulfilled' ? 
                (localResults.value?.timing?.local || 0) : 0;
            const aiTime = aiEnhancement.status === 'fulfilled' ? 
                (aiEnhancement.value?.timing?.ai || 0) : 0;

            this.metrics.record({
                localTime,
                aiTime,
                totalTime,
                cacheHit: 0,
                resultsCount: finalResults.length,
                localSuccess: localResults.status === 'fulfilled',
                aiSuccess: aiEnhancement.status === 'fulfilled'
            });
        }

        // 工具方法
        generateSearchId(query, language) {
            return `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}_${query.length}_${language}`;
        }

        normalizeQuery(query) {
            return query.trim().toLowerCase().replace(/\s+/g, ' ');
        }

        isSearchCancelled(searchId) {
            const search = this.activeSearches.get(searchId);
            return !search || search.cancelled;
        }

        cancelPreviousSearch() {
            this.activeSearches.forEach(search => {
                search.cancelled = true;
            });
            this.activeSearches.clear();
        }

        createTimeout(ms) {
            return new Promise((_, reject) => {
                setTimeout(() => reject(new Error(`Timeout after ${ms}ms`)), ms);
            });
        }

        // 增强统计信息获取
        getStats() {
            const ragStats = this.ragEngine?.getStats() || {
                initialized: false,
                documentsCount: 0,
                vocabularySize: 0,
                cacheSize: 0
            };
            
            const searchMetrics = this.metrics.getStats();

            return {
                overview: {
                    healthScore: this.metrics.calculateHealthScore(),
                    performanceGrade: this.getOverallPerformanceGrade(searchMetrics),
                    systemReady: this.ragInitialized && this.geminiAssistant?.isAvailable()
                },
                metrics: searchMetrics,
                cache: {
                    local: this.localCache.cache.size,
                    ai: this.aiCache.cache.size,
                    hitRate: searchMetrics.cacheHitRate
                },
                activeSearches: this.activeSearches.size,
                rag: {
                    initialized: this.ragInitialized,
                    engine: ragStats,
                    available: typeof RAGVectorEngine !== 'undefined',
                    performance: ragStats.performance || {}
                },
                components: {
                    dataManager: !!this.dataManager,
                    geminiAssistant: !!this.geminiAssistant && this.geminiAssistant.isAvailable(),
                    i18nManager: !!this.i18n,
                    unifiedEngine: true
                }
            };
        }
        
        getOverallPerformanceGrade(metrics) {
            const totalTime = metrics.avgTotalTime;
            const budget = this.metrics.performanceBudget.totalSearchTarget;
            
            if (totalTime <= budget * 0.5) return 'A';
            if (totalTime <= budget * 0.7) return 'B';
            if (totalTime <= budget) return 'C';
            if (totalTime <= budget * 1.5) return 'D';
            return 'F';
        }

        clearCache() {
            this.localCache.clear();
            this.aiCache.clear();
            
            if (this.ragEngine && this.ragEngine.clearCache) {
                this.ragEngine.clearCache();
            }
            
            console.log('Search cache cleared (including RAG cache)');
        }

        // 智能预加载常用搜索
        preloadCommonQueries(queries) {
            const currentLanguage = this.i18n.getCurrentLanguage();
            console.log(`开始预加载 ${queries.length} 个常用查询...`);
            
            let completed = 0;
            
            queries.forEach((query, index) => {
                setTimeout(() => {
                    this.search(query, currentLanguage, {
                        onComplete: (results) => {
                            completed++;
                            console.debug(`预加载完成 (${completed}/${queries.length}): "${query}" -> ${results.length} 个结果`);
                            
                            if (completed === queries.length) {
                                console.log(`所有预加载任务完成，缓存已温热`);
                            }
                        },
                        onError: (error) => {
                            completed++;
                            console.warn(`预加载失败: "${query}"`, error);
                        }
                    });
                }, index * 150); // 错开预加载时间，稍微增加间隔
            });
        }
        
        // 系统健康检查
        async performHealthCheck() {
            console.log('执行系统健康检查...');
            
            const healthReport = {
                timestamp: new Date().toISOString(),
                overall: 'healthy',
                components: {},
                issues: [],
                recommendations: []
            };
            
            // 检查数据管理器
            try {
                const dataStats = this.dataManager.getStats?.() || {};
                healthReport.components.dataManager = {
                    status: 'healthy',
                    questionsCount: dataStats.questionsCount || 0
                };
            } catch (error) {
                healthReport.components.dataManager = { status: 'error', error: error.message };
                healthReport.issues.push('数据管理器异常');
            }
            
            // 检查AI助手
            try {
                const aiAvailable = this.geminiAssistant?.isAvailable() || false;
                healthReport.components.aiAssistant = {
                    status: aiAvailable ? 'healthy' : 'warning',
                    available: aiAvailable
                };
                if (!aiAvailable) {
                    healthReport.issues.push('AI助手不可用');
                }
            } catch (error) {
                healthReport.components.aiAssistant = { status: 'error', error: error.message };
            }
            
            // 检查RAG引擎
            try {
                const ragStats = this.ragEngine?.getStats?.() || {};
                healthReport.components.ragEngine = {
                    status: this.ragInitialized ? 'healthy' : 'warning',
                    initialized: this.ragInitialized,
                    documentsCount: ragStats.documentsCount || 0,
                    performance: ragStats.performance || {}
                };
                if (!this.ragInitialized) {
                    healthReport.issues.push('RAG向量引擎未初始化');
                }
            } catch (error) {
                healthReport.components.ragEngine = { status: 'error', error: error.message };
            }
            
            // 检查性能指标
            const stats = this.getStats();
            const healthScore = stats.overview?.healthScore || 0;
            healthReport.components.performance = {
                status: healthScore > 0.8 ? 'healthy' : healthScore > 0.6 ? 'warning' : 'critical',
                healthScore,
                performanceGrade: stats.overview?.performanceGrade || 'N/A'
            };
            
            if (healthScore < 0.8) {
                healthReport.issues.push(`性能健康度低: ${(healthScore * 100).toFixed(1)}%`);
            }
            
            // 生成推荐建议
            if (stats.cache?.hitRate < 50) {
                healthReport.recommendations.push('考虑增加缓存大小或预加载常用查询');
            }
            
            if (stats.metrics?.avgTotalTime > 3000) {
                healthReport.recommendations.push('搜索响应时间过长，建议检查网络或优化算法');
            }
            
            // 决定整体状态
            if (healthReport.issues.length === 0) {
                healthReport.overall = 'healthy';
            } else if (healthReport.issues.length <= 2) {
                healthReport.overall = 'warning';
            } else {
                healthReport.overall = 'critical';
            }
            
            console.log('系统健康检查完成:', healthReport.overall);
            if (healthReport.issues.length > 0) {
                console.warn('发现问题:', healthReport.issues);
            }
            
            return healthReport;
        }
    }

    // 导出到全局
    if (typeof window !== 'undefined') {
        window.UnifiedSearchEngine = UnifiedSearchEngine;
    }
    
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = UnifiedSearchEngine;
    }
})();